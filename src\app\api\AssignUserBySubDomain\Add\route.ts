import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function POST(req: NextRequest) {
    try {
        type AuthenticatedUser = {
            Id: string;
        };

        const user = await verifyToken(req) as AuthenticatedUser;
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { SubDomainId, AssignUser } = await req.json();

        // Validate SubDomainId is an array
        if (!SubDomainId || !Array.isArray(SubDomainId) || SubDomainId.length === 0) {
            return NextResponse.json(
                { error: "SubDomainId must be a non-empty array" },
                { status: 400 }
            );
        }

        if (!AssignUser) {
            return NextResponse.json(
                { error: "AssignUser is required" },
                { status: 400 }
            );
        }

        const uniqueSubDomainIds = [...new Set(SubDomainId)];

        const existingUser = await prisma.adminUser.findFirst({
            where: {
                Id: AssignUser,
                IsDeleted: false
            },
            select: {
                Id: true
            }
        });

        if (!existingUser) {
            return NextResponse.json(
                { error: "User ID is invalid" },
                { status: 400 }
            );
        }

        // Check for existing mappings
        const existingMappings = await prisma.subDomainUserMappings.findMany({
            where: {
                SubDomainId: {
                    in: uniqueSubDomainIds
                },
                UserId: AssignUser
            },
            select: {
                SubDomainId: true
            }
        });

        const existingSubDomainId = existingMappings.map(mapping => mapping.SubDomainId);
        const newSubDomainId = uniqueSubDomainIds.filter(subDomainId => !existingSubDomainId.includes(subDomainId));

        // If all mappings already exist
        if (newSubDomainId.length === 0) {
            return NextResponse.json(
                {
                    success: false,
                    message: "All SubDomainId-UserId mappings already exist",
                    existingMappings: existingSubDomainId.length
                },
                { status: 409 }
            );
        }

        // Create mappings for SubDomainIds that don't already exist
        const createdMappings = [];
        for (const subDomainId of newSubDomainId) {
            const mapping = await prisma.subDomainUserMappings.create({
                data: {
                    SubDomainId: subDomainId,
                    UserId: AssignUser
                }
            });
            createdMappings.push(mapping);
        }

        return NextResponse.json(
            {
                success: true,
                message: "User mappings created successfully",
                NewMappings: createdMappings.length,
                existingMappings: existingSubDomainId.length,
            },
            { status: 201 }
        );
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}