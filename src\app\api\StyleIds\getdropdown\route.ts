import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        type AuthenticatedUser = {
            User_Type: string;
            Id: string;
        };

        const user = await verifyToken(req) as AuthenticatedUser;
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        let where: any = {
            IsDeleted: false
        };

        // Add user filter if not Super Admin
        if (user.User_Type.toLowerCase() !== 'super admin') {
            where.CreatedBy = user.Id;
        }

        const stylesWithUsers = await prisma.styleIds.findMany({
            where,
            orderBy: {
                CreatedAt: 'desc'
            },
            select: {
                Id: true,
                Name: true,
                StyleId: true,
            }
        });

        return NextResponse.json({
            success: true,
            data: stylesWithUsers,
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}