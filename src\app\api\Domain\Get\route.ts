import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';


export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        const page = parseInt(searchParams.get("page") || "1");
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const search = searchParams.get("q") || '';
        const orderBy = searchParams.get("orderBy") || "CreatedAt";
        const orderDir = (searchParams.get("orderDir") || "asc").toLowerCase() as 'asc' | 'desc';

        let skip: number | undefined;
        let limit: number | undefined;

        if (length == -1) {
            skip = undefined;
            limit = undefined;
        }
        else {
            const skipCount = (start == 1 ? 0 : start - 1) * length;
            limit = length;
            skip = (skipCount > 0 ? skipCount : 0);
        }

        // Build filter
        let where: any = {
            IsDeleted: false
        };

        if (search) {
            where.OR = [
                { Name: { contains: search, mode: 'insensitive' } },
                { Prefix: { contains: search, mode: 'insensitive' } },
            ];
        }

        // Validate allowed orderBy fields
        const allowedFields = ['Name', 'Prefix', 'CreatedAt', 'ShowUrlName', 'ChannelId'];
        const safeOrderBy = allowedFields.includes(orderBy) ? orderBy : 'CreatedAt';

        // Create case-insensitive sorting for text fields
        const caseInsensitiveFields = ['Name', 'Prefix', 'ShowUrlName'];
        const orderByClause = [];

        if (caseInsensitiveFields.includes(safeOrderBy)) {
            orderByClause.push({
                [safeOrderBy]: {
                    sort: orderDir,
                    nulls: 'last'
                }
            });
        } else {
            orderByClause.push({
                [safeOrderBy]: orderDir
            });
        }

        // Fetch data
        const DomainWithUsers = await prisma.domain.findMany({
            where,
            skip,
            take: limit,
            orderBy: orderByClause,
            select: {
                Id: true,
                Name: true,
                Prefix: true,
                ShowUrlName: true,
            }
        });

        // Count for pagination
        const recordsTotal = await prisma.domain.count({ where: { IsDeleted: false } });
        const recordsFiltered = await prisma.domain.count({ where });

        return NextResponse.json({
            success: true,
            data: DomainWithUsers,
            pagination: {
                draw: parseInt(searchParams.get("draw") || "1"),
                recordsFiltered,
                //recordsTotal,
                currentPageCount: DomainWithUsers.length,
                start,
                length,
                currentPage: page,
                totalPages: length === -1 ? 1 : Math.ceil(recordsFiltered / (length || 1)),
                hasNextPage: length === -1 ? false : start * length < recordsFiltered,
                hasPreviousPage: start > 1,
            }
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Internal Server Error", details: error instanceof Error ? error.message : String(error) },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}