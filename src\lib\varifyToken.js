import { jwtVerify, SignJWT } from 'jose';
import { NextResponse } from 'next/server';

const generateNewAccessToken = async (payload) => {
  const secret = new TextEncoder().encode(process.env.JWT_SECRET);

  return await new SignJWT({
    id: payload.id,
    email: payload.email,
    user_type: payload.user_type,
  })
    .setProtectedHeader({ alg: 'HS256' })
    .setExpirationTime('15m')
    .setIssuedAt()
    .sign(secret);
};

export const verifyToken = async (req) => {
  try {
    const accessToken = req.cookies.get('accessToken')?.value;
    const refreshToken = req.cookies.get('refreshToken')?.value;
    const secret = new TextEncoder().encode(process.env.JWT_SECRET);

    if (!accessToken) return null;

    try {
      const { payload } = await jwtVerify(accessToken, secret);
      return payload;
    } catch (err) {
      if (err.code === 'ERR_JWT_EXPIRED' && refreshToken) {
        try {
          const { payload: refreshPayload } = await jwtVerify(refreshToken, secret);
          const newAccessToken = await generateNewAccessToken(refreshPayload);

          const response = NextResponse.next();
          response.cookies.set('accessToken', newAccessToken, {
            httpOnly: true,
            path: '/',
          });

          return refreshPayload;
        } catch {
          return null;
        }
      }

      return null;
    }
  } catch {
    return null;
  }
};
