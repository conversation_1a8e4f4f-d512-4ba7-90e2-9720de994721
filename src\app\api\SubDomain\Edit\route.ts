import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';


export async function PUT(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { Id, Name, Url, Domain, AssignUsers, AccountId, CId, HeadTag, HeadTagScript,
            HeadTagScriptLandingPage, HeadTagScriptSearchPage, GId, AWId, SendTo } = await req.json();

        if (!Id) {
            return NextResponse.json(
                { error: "Id is required for updating a subdomain" },
                { status: 400 }
            );
        }
        if (AccountId && !Array.isArray(AccountId)) {
            return NextResponse.json(
                { error: 'AccountId must be an array' },
                { status: 400 }
            );
        }
        // Check if subdomain exists
        const existingSubdomain = await prisma.subDomain.findUnique({
            where: { Id }
        });

        if (!existingSubdomain) {
            return NextResponse.json(
                { error: "Subdomain not found" },
                { status: 404 }
            );
        }

        // Prepare update data
        const updateData: any = {
            UpdatedAt: new Date(),
        };

        if (Name !== undefined) updateData.Name = Name;
        if (Url !== undefined) updateData.Url = Url;
        if (Domain !== undefined) updateData.Domain = Domain;
        if (AccountId !== undefined) updateData.AccountId = AccountId;
        if (CId !== undefined) updateData.CId = CId;
        if (HeadTag !== undefined) updateData.HeadTag = HeadTag;
        if (HeadTagScript !== undefined) updateData.HeadTagScript = HeadTagScript;
        if (HeadTagScriptLandingPage !== undefined) updateData.HeadTagScriptLandingPage = HeadTagScriptLandingPage;
        if (HeadTagScriptSearchPage !== undefined) updateData.HeadTagScriptSearchPage = HeadTagScriptSearchPage;
        if (GId !== undefined) updateData.GId = GId;
        if (AWId !== undefined) updateData.AWId = AWId;
        if (SendTo !== undefined) updateData.SendTo = SendTo;

        // If name is changing, check for duplicates
        if (Name && Name !== existingSubdomain.Name) {
            const subdomainWithSameName = await prisma.subDomain.findFirst({
                where: {
                    Name,
                }
            });

            if (subdomainWithSameName) {
                return NextResponse.json(
                    { error: "Subdomain with this name already exists" },
                    { status: 409 }
                );
            }
        }

        // if (AssignUsers !== undefined) {
        //     if (!Array.isArray(AssignUsers)) {
        //         return NextResponse.json(
        //             { error: "AssignUsers must be an array" },
        //             { status: 400 }
        //         );
        //     }

        //     // Only validate user IDs if the array is not empty
        //     if (AssignUsers.length > 0) {
        //         const userIds = AssignUsers;
        //         const existingUsers = await prisma.AdminUser.findMany({
        //             where: {
        //                 Id: {
        //                     in: userIds
        //                 },
        //                 IsDeleted: false
        //             },
        //             select: {
        //                 Id: true
        //             }
        //         });

        //         if (existingUsers.length !== userIds.length) {
        //             const foundUserIds = existingUsers.map(user => user.Id);
        //             const invalidUserIds = userIds.filter(id => !foundUserIds.includes(id));

        //             return NextResponse.json(
        //                 {
        //                     error: "One or more user IDs are invalid",
        //                     invalidUserIds
        //                 },
        //                 { status: 400 }
        //             );
        //         }
        //     }
        // }



        if (!AssignUsers || !Array.isArray(AssignUsers) || AssignUsers.length === 0) {
            return NextResponse.json(
                { error: "AssignUsers must be a non-empty array" },
                { status: 400 }
            );
        }
        const result = await prisma.$transaction(async (prismaTransaction) => {
            const updatedSubdomain = await prismaTransaction.subDomain.update({
                where: { Id },
                data: updateData
            });

            // 2. Update user mappings if AssignUsers is provided
            let updatedMappings = [];
            if (AssignUsers !== undefined) {
                // Delete existing mappings
                await prismaTransaction.subDomainUserMappings.deleteMany({
                    where: {
                        SubDomainId: updatedSubdomain.Id
                    }
                });

                // Create new mappings
                for (const userId of AssignUsers) {
                    const mapping = await prismaTransaction.subDomainUserMappings.create({
                        data: {
                            SubDomainId: updatedSubdomain.Id,
                            UserId: userId
                        }
                    });
                    updatedMappings.push(mapping);
                }
            }

            return {
                subdomain: updatedSubdomain,
                mappings: updatedMappings
            };
        });

        return NextResponse.json(
            {
                success: true,
                message: "Subdomain updated successfully"
            },
            { status: 200 }
        );
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Internal Server Error", details: error instanceof Error ? error.message : String(error) },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}