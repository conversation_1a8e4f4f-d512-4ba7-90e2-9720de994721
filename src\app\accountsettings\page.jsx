import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import AccountSettings from "./_components/Account-Settings";
import { UploadPhotoForm } from "./_components/upload-photo";
// import UploadPhotoForm from "./_components/upload-photo";


// export const metadata: Metadata = {
//   title: "Settings Page",
// };

export default function SettingsPage() {
    return (
        <div className="mx-auto w-full">
            <Breadcrumb pageName="Settings" />

            <div className="grid grid-cols-5 gap-8">
                <div className="col-span-5 xl:col-span-3">
                    <AccountSettings />
                </div>
                <div className="col-span-5 xl:col-span-2">
                    <UploadPhotoForm />
                </div>
            </div>
        </div>
    );
};

