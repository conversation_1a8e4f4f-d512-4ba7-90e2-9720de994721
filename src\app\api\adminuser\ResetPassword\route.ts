import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcrypt';
import { verifyToken } from '../../../../lib/varifyToken';

const SALT_ROUNDS = 10;
const DEFAULT_ADMIN_PASSWORD = "Admin1234";

export async function POST(req: NextRequest) {
    try {
        // Authenticate user
        const user = await verifyToken(req);
        if (!user?.Id) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { userId, oldPassword, newPassword } = await req.json();

        const currentUser = await prisma.adminUser.findUnique({
            where: { Id: user.Id.toString() },
            select: {
                Password: true,
                User_Type: true
            }
        });

        // Admin reset another user's password
        if (userId) {
            // const admin = await prisma.adminUser.findUnique({
            //     where: { Id: user.Id.toString() },
            //     select: { User_Type: true }
            // });

            if (!currentUser || !['Admin', 'Super Admin'].includes(currentUser.User_Type ?? '')) {
                return NextResponse.json({ error: "Insufficient privileges" }, { status: 403 });
            }

            const hashedPassword = await bcrypt.hash(DEFAULT_ADMIN_PASSWORD, SALT_ROUNDS);

            await prisma.adminUser.update({
                where: { Id: userId.toString() },
                data: {
                    Password: hashedPassword,
                    UpdatedAt: new Date()
                }
            });

            return NextResponse.json({
                success: true,
                message: `Password reset to default (${DEFAULT_ADMIN_PASSWORD}) successfully.`
            }, { status: 200 });
        }

        // User changes own password
        if (!oldPassword || !newPassword) {
            return NextResponse.json({ error: "Both current and new passwords are required" }, { status: 400 });
        }

        if (oldPassword === newPassword) {
            return NextResponse.json({ error: "New password must be different from current password" }, { status: 400 });
        }

        if (newPassword.length < 8) {
            return NextResponse.json({ error: "Password must be at least 8 characters long" }, { status: 400 });
        }

        // const currentUser = await prisma.adminUser.findUnique({
        //     where: { Id: user.Id.toString() },
        //     select: { Password: true }
        // });

        if (!currentUser || !currentUser.Password) {
            return NextResponse.json({ error: "User not found or password not set" }, { status: 404 });
        }

        const isPasswordValid = await bcrypt.compare(oldPassword, currentUser.Password);
        if (!isPasswordValid) {
            return NextResponse.json({ error: "Current password is incorrect" }, { status: 401 });
        }

        const hashedPassword = await bcrypt.hash(newPassword, SALT_ROUNDS);

        await prisma.adminUser.update({
            where: { Id: user.Id.toString() },
            data: {
                Password: hashedPassword,
                UpdatedAt: new Date()
            }
        });

        return NextResponse.json({ success: true, message: "Password updated successfully" }, { status: 200 });

    } catch (error) {
        console.error("Password change error:", error);
        return NextResponse.json({
            error: "Internal server error",
            details: process.env.NODE_ENV === 'development' ? error : undefined
        }, { status: 500 });
    } finally {
        await prisma.$disconnect();
    }
}
