import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const users = await prisma.channals.findMany({
            where: {
                Active: true,
            },
            orderBy: {
                CreatedAt: 'desc',
            },
            select: {
                Id: true,
                Name: true,
                DisplayName: true,
            },
        });

        const dataWithCustomChannelId = users.map(channel => {
            const parts = (channel.Name ?? "").split('/');
            const customChannelId = parts[parts.length - 1];
            return {
                ...channel,
                CustomChannelId: customChannelId,
            };
        });

        return NextResponse.json({
            success: true,
            data: dataWithCustomChannelId,
        });

    } catch (error) {
        console.error("Error fetching users:", error);
        return NextResponse.json(
            { error: "Failed to fetch users" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}
