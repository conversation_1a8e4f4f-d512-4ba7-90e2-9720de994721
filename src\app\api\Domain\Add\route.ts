import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import bcrypt from 'bcrypt';


export async function POST(req: NextRequest) {
    try {
        type AuthenticatedUser = {
            Id: string;
        };
        const user = await verifyToken(req) as AuthenticatedUser;
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { Name, Prefix, ShowUrlName, ChannelId, CookieMinutes, StyleIdDm, StyleIdLm, HeadTagScript,
            HeadTagScriptLandingPage, HeadTagScriptSearchPage, GId, AWId, SendTo } = await req.json();

        if (!Name || !ShowUrlName) {
            return NextResponse.json(
                { error: "ShowUrlName and Name are required" },
                { status: 400 }
            );
        }

        const existing = await prisma.domain.findFirst({
            where: {
                Name,
                IsDeleted: false
            }
        });

        if (existing) {
            return NextResponse.json(
                { error: "Name already exists" },
                { status: 409 }
            );
        }

        const newDomain = await prisma.domain.create({
            data: {
                Name,
                Prefix,
                ShowUrlName,
                CreatedBy: user.Id,
                ChannelId,
                CookieMinutes,
                StyleIdDm,
                StyleIdLm,
                HeadTagScript,
                HeadTagScriptLandingPage,
                HeadTagScriptSearchPage,
                GId,
                AWId,
                SendTo
            }
        })

        return NextResponse.json(
            {
                success: true,
                message: "Domain created successfully",
                data: newDomain
            },
            { status: 201 }
        );
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}