import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        // Authentication check
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Extract query parameters
        const { searchParams } = new URL(req.url);
        const articleId = searchParams.get("articleId");
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");

        // Validate articleId (UUID)
        if (!articleId || !/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(articleId)) {
            return NextResponse.json(
                { error: "Valid ArticleId (UUID) is required" },
                { status: 400 }
            );
        }

        // Step 1: Get campaign mappings with related data in a single query
        const results = await prisma.articleCampaignMappings.findMany({
            where: {
                ArticleId: articleId,
                CampaignId: { not: null }
            },
            include: {
                Ads_Campaigns: {
                    select: {
                        CampaignId: true,
                        Name: true
                    }
                }
            },
            orderBy: {
                CreatedAt: 'desc'
            }
        });

        if (!results.length) {
            return NextResponse.json({
                success: true,
                data: [],
                pagination: {
                    recordsTotal: 0,
                    recordsFiltered: 0,
                    currentPageCount: 0,
                    draw: parseInt(searchParams.get("draw") || "1"),
                    start,
                    length,
                    currentPage: start,
                    totalPages: 1,
                    hasNextPage: false,
                    hasPreviousPage: false,
                },
            });
        }

        // Transform results to match expected format
        const formattedResults = results.map(mapping => ({
            CampaignId: mapping.Ads_Campaigns?.CampaignId?.toString(),
            Name: mapping.Ads_Campaigns?.Name || "N/A",
            CreatedAt: mapping.CreatedAt
        }));

        // Pagination
        const recordsTotal = formattedResults.length;
        const paginatedData = length === -1
            ? formattedResults
            : formattedResults.slice((start - 1) * length, start * length);

        return NextResponse.json({
            success: true,
            data: paginatedData,
            pagination: {
                draw: parseInt(searchParams.get("draw") || "1"),
                recordsTotal,
                recordsFiltered: recordsTotal,
                currentPageCount: paginatedData.length,
                start,
                length,
                currentPage: start,
                totalPages: length === -1 ? 1 : Math.ceil(recordsTotal / length),
                hasNextPage: length === -1 ? false : start * length < recordsTotal,
                hasPreviousPage: start > 1,
            },
        });

    } catch (error) {
        console.error("Error fetching campaigns:", error);
        return NextResponse.json(
            { error: "Failed to fetch campaigns" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}