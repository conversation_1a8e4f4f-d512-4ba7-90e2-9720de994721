export const trimObject = (obj) => {
  return Object.keys(obj).reduce((acc, key) => {
    const value = obj[key];
    acc[key] = typeof value === "string" ? value?.trim() : value;
    return acc;
  }, {});
};

export const themeChanger = (mode) => {
  return mode === "dark" ? "dark" : null;
};

export async function readTokensFromFile(TOKENS_PATH, fs) {
  try {
    const data = await fs.readFile(TOKENS_PATH, "utf8");
    return JSON.parse(data);
  } catch (error) {
    console.error("Error reading tokens file:", error);
    return null;
  }
}

export const convertToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.readAsDataURL(file);

    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
};


export async function saveTokensToFile(tokens, TOKENS_PATH, fs) {
  try {
    await fs.writeFile(TOKENS_PATH, JSON.stringify(tokens), {
      encoding: "utf8",
    });
  } catch (error) {
    console.error("Error writing tokens to file:", error);
  }
}
export async function getAdSenseAccounts(oauth2Client, google) {
  const adsense = google.adsense({ version: "v2", auth: oauth2Client });
  const res = await adsense?.accounts?.list({});
  return res?.data?.accounts;
}

export async function getAdClients(oauth2Client, accountId, google) {
  const adsense = google.adsense({ version: "v2", auth: oauth2Client });
  const res = await adsense?.accounts?.adclients?.list({
    parent: accountId,
  });
  return res?.data?.adClients;
}
export async function getCustomChannels(oauth2Client, adClientId, google) {
  const adsense = google.adsense({ version: "v2", auth: oauth2Client });
  const res = await adsense?.accounts?.adclients?.customchannels?.list({
    parent: adClientId,
  });
  return res?.data?.customChannels;
}
export async function createCustomChannelId(oauth2Client, accountId, google) {
  const adsense = google.adsense({ version: "v2", auth: oauth2Client });
  const res = await adsense?.accounts?.adclients?.customchannels?.create({
    parent: accountId,
    requestBody: {
      displayName: "Custom Channel-Goti",
      active: true,
    },
  });
  return res;
}

export async function getSavedReports(oauth2Client, accountId, google) {
  const adsense = google.adsense({ version: "v2", auth: oauth2Client });
  const res = await adsense?.accounts?.reports?.saved?.list({
    parent: accountId,
  });
  return res?.data?.savedReports;
}

export async function generateSavedReport(
  oauth2Client,
  savedReportId,
  startDate,
  endDate,
  google,
  toggle
) {
  const adsense = google?.adsense({ version: "v2", auth: oauth2Client });
  const res = await adsense?.accounts?.reports?.saved?.generate({
    "startDate.year": startDate.year,
    "startDate.day": startDate.day,
    "startDate.month": startDate.month,
    "endDate.year": endDate.year,
    "endDate.day": endDate.day,
    "endDate.month": endDate.month,
    name: savedReportId,
    currencyCode: toggle === "false" ? "USD" : "INR",
  });

  return res?.data;
}

export async function generateReport(
  oauth2Client,
  accountId,
  google,
  startDate,
  endDate,
  metrics,
  breakPoints,
  selectedOptions,
  toggle,
  selectedChannels,
  selectedStyles
) {
  const breakPointsData = [
    ...(breakPoints?.country ? ["COUNTRY_CODE"] : []),
    ...(breakPoints?.platform ? ["PLATFORM_TYPE_NAME"] : []),
    ...(breakPoints?.date ? ["DATE"] : selectedOptions && selectedOptions?.length > 0 && !breakPoints?.date ? ["DATE"] : []),
    ...(breakPoints?.customChannel ? ["CUSTOM_CHANNEL_NAME", "CUSTOM_CHANNEL_ID"] : []),
    ...(breakPoints?.styleId ? ["CUSTOM_SEARCH_STYLE_NAME", "CUSTOM_SEARCH_STYLE_ID"] : [])
  ];
  const adsense = google?.adsense({ version: "v2", auth: oauth2Client });
  const filters = [];
  if (selectedChannels?.length) {
    filters.push(selectedChannels?.length > 1 ? [selectedChannels?.map((selected) => `CUSTOM_CHANNEL_ID==${selected}`)?.toString()] : selectedChannels?.map((selected) => `CUSTOM_CHANNEL_ID==${selected}`)
    );
  }
  if (selectedStyles?.length) {
    filters.push(selectedStyles?.length > 1 ? [selectedStyles?.map((selected) => `CUSTOM_SEARCH_STYLE_ID==${selected}`)?.toString()] : selectedStyles?.map((selected) => `CUSTOM_SEARCH_STYLE_ID==${selected}`)
    );
  }
  const genratedObject = {
    account: accountId,
    "startDate.year": startDate.year,
    "startDate.day": startDate.day,
    "startDate.month": startDate.month,
    "endDate.year": endDate.year,
    "endDate.day": endDate.day,
    "endDate.month": endDate.month,
    dimensions: [...breakPointsData],
    metrics: metrics,
    currencyCode: toggle === "false" ? "USD" : "INR",
    // filters: selectedOptions?.length > 1 ? [selectedOptions?.map((selected) => `CUSTOM_CHANNEL_ID==${selected}`)?.toString()] : selectedOptions?.map((selected) => `CUSTOM_CHANNEL_ID==${selected}`)
    // filters: selectedOptions?.length > 1 ? [selectedOptions?.map((selected) => `CUSTOM_SEARCH_STYLE_ID==${selected}`)?.toString()] : selectedOptions?.map((selected) => `CUSTOM_SEARCH_STYLE_ID==${selected}`)
    filters: filters
  }
  const res = await adsense?.accounts?.reports?.generateCsv(genratedObject);
  return res?.data;
}

export function getLast7Days() {
  const days = [];
  const today = new Date();

  for (let i = 6; i >= 0; i--) {
    const day = new Date();
    day.setDate(today.getDate() - i);
    days.push(day.toISOString().split("T")[0]);
  }

  return days;
}
export function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
}

export const getCurrency = (curr) => {
  switch (curr) {
    case "USD":
      return "$";
      break;
    case "INR":
      return "₹";
      break;
    default:
      return "$";
  }
};

// export const deleteImage = async (imageUrl, path, fs) => {
//   const imagePath = path.resolve(process.cwd(), 'public', 'uploads', imageUrl?.split('uploads')[1]);
//   const fileName = path.basename(imagePath, path.extname(imagePath));
//   const fileExtension = path.extname(imagePath);
//   const originalImagePath = path.resolve(process.cwd(), 'public', 'uploads', fileName + fileExtension);
//   const smallImagePath = path.resolve(process.cwd(), 'public', 'uploads', fileName + "_small" + fileExtension);
//   const mediumImagePath = path.resolve(process.cwd(), 'public', 'uploads', fileName + "_medium" + fileExtension);

//   try {
//     if(!imageUrl?.includes("cloudinary")){
//       if (fs.existsSync(originalImagePath)) {
//         await fs.promises.unlink(originalImagePath);
//         console.log('Original image deleted successfully!');
//       }
//       if (fs.existsSync(smallImagePath)) {
//         await fs.promises.unlink(smallImagePath);
//         console.log('Small image deleted successfully!');
//       }
//       if (fs.existsSync(mediumImagePath)) {
//         await fs.promises.unlink(mediumImagePath);
//         console.log('medium image deleted successfully!');
//       }
//       return 'Both images deleted successfully!';
//     }
//   } catch (error) {
//     throw new Error('Error deleting the images: ' + error.message);
//   }
// };

// export const fetchImage = (type, imageUrl) => {

//   const lastDotIndex = imageUrl.lastIndexOf(".");
//   switch (type) {
//     case "small":
//       if (lastDotIndex !== -1) {
//         imageUrl =
//           imageUrl.slice(0, lastDotIndex) +
//           "_small" +
//           imageUrl.slice(lastDotIndex);
//         return imageUrl;
//       }
//       break;
//     case "medium":
//       if (lastDotIndex !== -1) {
//         imageUrl =
//           imageUrl.slice(0, lastDotIndex) +
//           "_medium" +
//           imageUrl.slice(lastDotIndex);
//         return imageUrl;
//       }
//       break;

//     case "cloud":
//       return imageUrl;
//       break;
//     default:
//       return imageUrl;
//       break;
//   }
// };
export const deleteImage = async (imageUrl, path, fs) => {
  const UPLOAD_DIR = process.env.IMAGE_STORAGE_PATH || "/var/www/images";
  const imagePath = path.resolve(UPLOAD_DIR, imageUrl.replace(/^\//, '')); // Remove leading slash
  const fileName = path.basename(imagePath, path.extname(imagePath));
  const fileExtension = path.extname(imagePath);
  const originalImagePath = path.resolve(UPLOAD_DIR, fileName + fileExtension);
  const smallImagePath = path.resolve(UPLOAD_DIR, fileName + "_small" + fileExtension);
  const mediumImagePath = path.resolve(UPLOAD_DIR, fileName + "_medium" + fileExtension);

  try {
    if (!imageUrl?.includes("cloudinary")) {
      if (fs.existsSync(originalImagePath)) {
        await fs.promises.unlink(originalImagePath);
        console.log('Original image deleted successfully!');
      }
      if (fs.existsSync(smallImagePath)) {
        await fs.promises.unlink(smallImagePath);
        console.log('Small image deleted successfully!');
      }
      if (fs.existsSync(mediumImagePath)) {
        await fs.promises.unlink(mediumImagePath);
        console.log('Medium image deleted successfully!');
      }
      return 'All images deleted successfully!';
    }
  } catch (error) {
    throw new Error('Error deleting the images: ' + error.message);
  }
};

export const fetchImage = (type, imageUrl) => {
  const IMAGE_ACCESS_DOMAIN = process.env.NEXT_PUBLIC_IMAGE_ACCESS_DOMAIN || "http://img.findbestanswer.com/";
  if (imageUrl.includes("cloudinary")) {
    return imageUrl; // Return Cloudinary URL as is
  }

  const lastDotIndex = imageUrl.lastIndexOf(".");
  let modifiedUrl = imageUrl;

  switch (type) {
    case "small":
      if (lastDotIndex !== -1) {
        modifiedUrl =
          imageUrl.slice(0, lastDotIndex) +
          "_small" +
          imageUrl.slice(lastDotIndex);
      }
      break;
    case "medium":
      if (lastDotIndex !== -1) {
        modifiedUrl =
          imageUrl.slice(0, lastDotIndex) +
          "_medium" +
          imageUrl.slice(lastDotIndex);
      }
      break;
    default:
      modifiedUrl = imageUrl;
      break;
  }

  return `${IMAGE_ACCESS_DOMAIN}${modifiedUrl}`; // Prepend domain
};

export function decodeJWT(token) {
  try {
    const base64Url = token?.split('.')[1]; // Get payload part
    const base64 = base64Url?.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(atob(base64).split('').map(c =>
      '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
    ).join(''));

    return JSON.parse(jsonPayload);
  } catch (e) {
    console.error("Invalid JWT", e);
    return null;
  }
}