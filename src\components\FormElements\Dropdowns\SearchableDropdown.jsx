"use client";
import { useEffect, useRef, useState } from "react";
import { GoTriangleUp, GoTriangleDown } from "react-icons/go";
import { IoCloseOutline } from "react-icons/io5";

const SearchableDropdown = ({
  label,
  options = [],
  placeholder = "Search...",
  value,
  onChange,
  error = "",
  displayKey = "",
  displayKey2 = "",
  idKey = "Id",
}) => {
  const dropdownRef = useRef(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Find the selected item to display its name
  const selectedItem = options.find((item) => item[idKey] === value);

  const handleSelect = (item) => {
    onChange(item);
    setSearchTerm("");
    setIsDropdownOpen(false);
  };

  const handleClear = (e) => {
    e.stopPropagation(); // Prevent the dropdown from toggling
    onChange(null); // Clear the selection
    setSearchTerm("");
    setIsDropdownOpen(false); // Keep dropdown closed
  };

  const filteredOptions = options.filter(
    (item) =>
      item[displayKey]?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item[idKey]?.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
        setSearchTerm("");
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div className="mb-3 w-full">
      <label className="mb-4 block text-sm font-bold text-dark dark:text-white">
        {label}
      </label>
      <div className="relative" ref={dropdownRef}>
        <button
          type="button"
          className="flex w-full items-center justify-between rounded-lg border-[1.5px] border-stroke bg-transparent px-5 py-3 text-left outline-none transition-all duration-200 focus:border-primary dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary"
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        >
          <span className="flex-1 text-gray-500 dark:text-gray-400">
            {selectedItem ? selectedItem[displayKey] : placeholder}
          </span>
          <div className="flex items-center gap-2">
            {selectedItem && (
              <span
                className="cursor-pointer text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                onClick={handleClear}
              >
                <IoCloseOutline size={16} />
              </span>
            )}
            {isDropdownOpen ? <GoTriangleUp /> : <GoTriangleDown />}
          </div>
        </button>

        {isDropdownOpen && (
          <div className="absolute z-10 mt-1 w-full rounded-lg border border-stroke bg-white shadow-lg dark:border-dark-3 dark:bg-gray-dark">
            <div className="p-2">
              <input
                type="text"
                className="w-full rounded-lg border-[1.5px] border-stroke bg-transparent px-3 py-2 text-sm outline-none focus:border-primary dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary"
                placeholder="Search..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                autoFocus
              />
            </div>
            <div className="max-h-50 overflow-y-auto">
              {filteredOptions.length > 0 ? (
                filteredOptions.map((item) => (
                  <div
                    key={item[idKey]}
                    className={`cursor-pointer px-3 py-1 hover:bg-gray-100 dark:hover:bg-dark-3 ${
                      value === item[idKey] ? "bg-primary/10 text-primary" : ""
                    }`}
                    onClick={() => handleSelect(item)}
                  >
                    {`${item[displayKey] || ""}${item[displayKey2] ? ` (${item[displayKey2]})` : ""}`}
                  </div>
                ))
              ) : (
                <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                  No options found
                </div>
              )}
            </div>
          </div>
        )}
      </div>
      {error && <p className="mt-1 text-sm text-red-500">{error}</p>}
    </div>
  );
};

export default SearchableDropdown;