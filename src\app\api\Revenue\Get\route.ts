import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import { google } from "googleapis";
import {
    generateReport,
    getAdClients,
    getAdSenseAccounts,
    readTokensFromFile,
    saveTokensToFile
} from '@/utils/functions';
import fs from "fs/promises";
import path from 'path';

const TOKENS_PATH = path.join(process.cwd(), "public", "tokens.json");
const BATCH_SIZE = 200;

export async function GET(req: NextRequest) {
    try {
        // Verify user authentication
        const user = await verifyToken(req);
        if (!user?.Id) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Extract query parameters
        const { searchParams } = new URL(req.url);
        const code = searchParams.get("code");
        const startDateParams = searchParams.get("startDate");
        const endDateParams = searchParams.get("endDate");
        const breakPoints = searchParams.get("breakPoints") || "{}";
        const selectedOptions = searchParams.get("selectedOptions") || "[]";
        const toggle = searchParams.get("toggle") || "false";
        const selectedChannels = JSON.parse(searchParams.get("selectedChannels") || "[]");
        const selectedStyles = JSON.parse(searchParams.get("selectedStyles") || "[]");

        // Initialize OAuth2 client
        const oauth2Client = new google.auth.OAuth2(
            process.env.CLIENT_ID,
            process.env.CLIENT_SECRET,
            process.env.REDIRECT_URI
        );

        // Handle OAuth2 flow
        const authUrl = oauth2Client.generateAuthUrl({
            access_type: "offline",
            scope: [
                "https://www.googleapis.com/auth/adsense.readonly",
                "https://www.googleapis.com/auth/adsense",
            ],
            prompt: "consent",
        });

        let tokens = await readTokensFromFile(TOKENS_PATH, fs);
        if (!code && !tokens) {
            return NextResponse.redirect(authUrl);
        }

        if (code) {
            const { tokens: newTokens } = await oauth2Client.getToken(code);
            oauth2Client.setCredentials(newTokens);
            tokens = newTokens;
            await saveTokensToFile({ ...newTokens, authUrl }, TOKENS_PATH, fs);

            return NextResponse.json({
                message: "Authorization successful",
                tokens: newTokens,
            }, { status: 200 });
        }

        // Refresh tokens if expired
        if (tokens?.expiry_date && tokens.expiry_date <= Date.now()) {
            oauth2Client.setCredentials({ refresh_token: tokens.refresh_token });
            const { credentials } = await oauth2Client.refreshAccessToken();
            tokens = { ...tokens, ...credentials };
            await saveTokensToFile(tokens, TOKENS_PATH, fs);
        }

        oauth2Client.setCredentials(tokens);

        // Get AdSense accounts
        const accounts = await getAdSenseAccounts(oauth2Client, google);
        if (!accounts || accounts.length === 0) {
            return NextResponse.json({ message: "No AdSense accounts found" }, { status: 404 });
        }

        // Get user settings
        const userSettings = await prisma.adminUserSetting.findFirst();
        if (!userSettings?.AdsAccountId || !userSettings?.AdsClientId) {
            return NextResponse.json({ message: "AdSense account or client ID not found" }, { status: 404 });
        }

        const exampleAccountId = userSettings.AdsAccountId;

        // Helper functions to fetch channel and style data
        const fetchChannelsRecord = async (Id: string) => {
            const record = await prisma.channals.findUnique({ where: { Id: Id } });
            return userSettings?.PubId
                ? `partner-pub-${userSettings.PubId}:${record?.ReportingDimensionId?.split(":")[1]}`
                : record?.ReportingDimensionId;
        };

        const fetchStyleDimensionId = async (Id: string) => {
            const record = await prisma.styleIds.findUnique({ where: { Id: Id } });
            return record?.StyleId;
        };

        // Get ad clients
        const adClients = await getAdClients(oauth2Client, exampleAccountId, google);
        if (!adClients || adClients.length === 0) {
            return NextResponse.json({ message: "No ad clients found" }, { status: 404 });
        }

        // Verify user exists and has proper permissions
        const finduser = await prisma.adminUser.findUnique({
            where: { Id: user.Id.toString() }
        });

        if (!finduser) {
            return NextResponse.json({ error: "User  not found." }, { status: 404 });
        }

        if (startDateParams && endDateParams) {
            // Prepare date ranges
            const finalStartDate = {
                day: startDateParams.split("-")[2],
                month: startDateParams.split("-")[1],
                year: startDateParams.split("-")[0],
            };

            const finalEndDate = {
                day: endDateParams.split("-")[2],
                month: endDateParams.split("-")[1],
                year: endDateParams.split("-")[0],
            };

            const metrics = [
                "ESTIMATED_EARNINGS",
                "IMPRESSIONS",
                "IMPRESSIONS_RPM",
                "CLICKS",
                "IMPRESSIONS_CTR",
                "COST_PER_CLICK",
            ];

            // Check user permissions
            if (finduser.User_Type && ["Admin", "Super Admin", "Account", "Partner"].includes(finduser.User_Type)) {
                let allReportsData: string[] = [];
                let headerRow = '';

                // Process selected channels in batches
                if (selectedChannels.length > 0) {
                    const totalBatches = Math.ceil(selectedChannels.length / BATCH_SIZE);

                    for (let i = 0; i < totalBatches; i++) {
                        const startIdx = i * BATCH_SIZE;
                        const endIdx = (i + 1) * BATCH_SIZE;
                        const batch = selectedChannels.slice(startIdx, endIdx);

                        console.log(`Processing batch ${i + 1}/${totalBatches} with ${batch.length} channels`);

                        // Process current batch
                        const channelDimensions = await Promise.all(
                            batch.map(async (channelId: string) => fetchChannelsRecord(channelId))
                        );

                        const validChannels = channelDimensions.filter(Boolean);

                        if (validChannels.length === 0) {
                            console.log(`Skipping batch ${i + 1} - no valid channels`);
                            continue;
                        }

                        // Generate report for current batch
                        const batchReport = await generateReport(
                            oauth2Client,
                            exampleAccountId,
                            google,
                            finalStartDate,
                            finalEndDate,
                            metrics,
                            JSON.parse(breakPoints),
                            validChannels,
                            toggle
                        );

                        if (typeof batchReport === 'string') {
                            const [currentHeader, ...rows] = batchReport.split('\n');

                            // Store header from first batch
                            if (i === 0) {
                                headerRow = currentHeader;
                                allReportsData.push(...rows);
                            } else {
                                allReportsData.push(...rows);
                            }
                        }
                    }
                }

                // Process selected styles (not batched as they're typically fewer)
                if (selectedStyles.length > 0) {
                    const styleDimensions = await Promise.all(
                        selectedStyles.map(async (styleId: string) => fetchStyleDimensionId(styleId))
                    );

                    const validStyles = styleDimensions.filter(Boolean);

                    if (validStyles.length > 0) {
                        const styleReport = await generateReport(
                            oauth2Client,
                            exampleAccountId,
                            google,
                            finalStartDate,
                            finalEndDate,
                            metrics,
                            JSON.parse(breakPoints),
                            validStyles,
                            toggle
                        );

                        if (typeof styleReport === 'string') {
                            const [currentHeader, ...rows] = styleReport.split('\n');

                            if (!headerRow) {
                                headerRow = currentHeader;
                            }
                            allReportsData.push(...rows);
                        }
                    }
                }

                // Combine all reports
                const combinedReport = [headerRow, ...allReportsData].join('\n');

                // Process the combined report data and store in database
                if (combinedReport) {
                    const rows = combinedReport.split('\n').slice(1); // Skip header row
                    const batchUpserts = [];

                    for (const row of rows) {
                        if (!row.trim()) continue;

                        // Parse CSV row based on the actual format:
                        // COUNTRY_NAME,PLATFORM_TYPE_NAME,DATE,CUSTOM_CHANNEL_NAME,CUSTOM_CHANNEL_ID,CUSTOM_SEARCH_STYLE_NAME,CUSTOM_SEARCH_STYLE_ID,ESTIMATED_EARNINGS,IMPRESSIONS,IMPRESSIONS_RPM,CLICKS,IMPRESSIONS_CTR,COST_PER_CLICK
                        const columns = row.split(',');

                        if (columns.length < 13) {
                            console.warn(`Skipping malformed row: ${row}`);
                            continue;
                        }

                        const [
                            countryName,
                            platformTypeName,
                            date,
                            customChannelName,
                            customChannelId,
                            customSearchStyleName,
                            customSearchStyleId,
                            estimatedEarnings,
                            impressions,
                            impressionsRpm,
                            clicks,
                            impressionsCtr,
                            costPerClick
                        ] = columns;

                        // Helper function to process values
                        const processValue = (value: string) =>
                            value === '' || value === 'unknown' || value === undefined ? null : value.trim();

                        const processNumericValue = (value: string) => {
                            const processed = processValue(value);
                            return processed ? parseFloat(processed) : null;
                        };

                        // Format date
                        const formattedDate = date ? new Date(date) : null;

                        // Use the actual channel ID and style ID from the CSV
                        const finalChannelId = processValue(customChannelId);
                        const finalStyleId = processValue(customSearchStyleId);

                        // Skip if essential fields are missing
                        if (!formattedDate) {
                            console.warn(`Skipping row with invalid date: ${date}`);
                            continue;
                        }

                        // Prepare upsert data
                        const uniqueIdentifier = {
                            Date: formattedDate,
                            StyleId: finalStyleId || "",  // Provide a default value if null
                            ChannalId: finalChannelId || "" // Provide a default value if null
                        };

                        // Check if both StyleId and ChannalId are valid before proceeding
                        if (!uniqueIdentifier.StyleId || !uniqueIdentifier.ChannalId) {
                            console.warn(`Skipping upsert due to missing identifiers: ${JSON.stringify(uniqueIdentifier)}`);
                            continue; // Skip this iteration if identifiers are not valid
                        }

                        const revenueData = {
                            Country: processValue(countryName),
                            PlatfromType: processValue(platformTypeName),
                            EstimatedEarnings: processNumericValue(estimatedEarnings),
                            Impressions: processNumericValue(impressions),
                            ImpressionsRpm: processNumericValue(impressionsRpm),
                            Clicks: processNumericValue(clicks),
                            ImpressionsCtr: processNumericValue(impressionsCtr),
                            CostPerClick: processNumericValue(costPerClick)
                        };

                        // Add to batch
                        batchUpserts.push(
                            prisma.revenue.upsert({
                                where: {
                                    Date_StyleId_ChannalId: uniqueIdentifier
                                },
                                create: {
                                    ...uniqueIdentifier,
                                    ...revenueData
                                },
                                update: revenueData
                            })
                        );
                    }

                    // Execute all upserts in a transaction
                    if (batchUpserts.length > 0) {
                        await prisma.$transaction(batchUpserts);
                        console.log(`Successfully upserted ${batchUpserts.length} records`);
                    }
                }

                // Return the combined CSV data
                return new NextResponse(combinedReport, {
                    status: 200,
                    headers: {
                        'Content-Type': 'text/csv',
                        'Content-Disposition': 'attachment; filename="adsense_report.csv"'
                    }
                });
            }
        }

        return NextResponse.json({ message: "Missing date range or unauthorized role" }, { status: 400 });

    } catch (error) {
        console.error("Adsense GET Error:", error);
        return NextResponse.json({ error: "Internal server error" }, { status: 500 });
    } finally {
        await prisma.$disconnect();
    }
}