import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import { google } from "googleapis";
import {
    generateReport,
    getAdClients,
    getAdSenseAccounts,
    readTokensFromFile,
    saveTokensToFile
} from '@/utils/functions';
import fs from "fs/promises";
import path from 'path';

const TOKENS_PATH = path.join(process.cwd(), "public", "tokens.json");
// const TOKENS_PATH = path.join(process.cwd(), "public", "tokens1.json");

export async function POST(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user?.Id) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        const code = searchParams.get("code");
        const startDateParams = searchParams.get("startDate");
        const endDateParams = searchParams.get("endDate");
        const breakPoints = searchParams.get("breakPoints") || "{}";
        const toggle = searchParams.get("toggle") || "false";

        const body = await req.json();
        const selectedChannels = body.selectedChannels || [];
        const selectedStyles = body.selectedStyles || [];

        const oauth2Client = new google.auth.OAuth2(
            process.env.CLIENT_ID,
            process.env.CLIENT_SECRET,
            process.env.REDIRECT_URI
        );

        const authUrl = oauth2Client.generateAuthUrl({
            access_type: "offline",
            scope: [
                "https://www.googleapis.com/auth/adsense.readonly",
                "https://www.googleapis.com/auth/adsense",
            ],
            prompt: "consent",
        });

        let tokens = await readTokensFromFile(TOKENS_PATH, fs);
        if (!code && !tokens) return NextResponse.redirect(authUrl);

        if (code) {
            const { tokens: newTokens } = await oauth2Client.getToken(code);
            oauth2Client.setCredentials(newTokens);
            tokens = newTokens;
            await saveTokensToFile({ ...newTokens, authUrl }, TOKENS_PATH, fs);
            return NextResponse.json({ message: "Authorization successful", tokens: newTokens }, { status: 200 });
        }

        if (tokens?.expiry_date && tokens.expiry_date <= Date.now()) {
            oauth2Client.setCredentials({ refresh_token: tokens.refresh_token });
            const { credentials } = await oauth2Client.refreshAccessToken();
            tokens = { ...tokens, ...credentials };
            await saveTokensToFile(tokens, TOKENS_PATH, fs);
        }

        oauth2Client.setCredentials(tokens);

        const accounts = await getAdSenseAccounts(oauth2Client, google);
        if (!accounts || accounts.length === 0) {
            return NextResponse.json({ message: "No AdSense accounts found" }, { status: 404 });
        }

        const userSettings = await prisma.adminUserSetting.findFirst();
        if (!userSettings?.AdsAccountId || !userSettings?.AdsClientId) {
            return NextResponse.json({ message: "AdSense account or client ID not found" }, { status: 404 });
        }

        const exampleAccountId = userSettings.AdsAccountId;

        const fetchChannelsRecord = async (Id: string) => {
            const record = await prisma.channals.findUnique({ where: { Id: Id } });
            return userSettings?.PubId
                ? `partner-pub-${userSettings.PubId}:${record?.ReportingDimensionId?.split(":")[1]}`
                : record?.ReportingDimensionId;
        };

        const fetchStyleDimensionId = async (Id: string) => {
            const record = await prisma.styleIds.findUnique({ where: { Id: Id } });
            return record?.StyleId;
        };

        const adClients = await getAdClients(oauth2Client, exampleAccountId, google);
        if (!adClients || adClients.length === 0) {
            return NextResponse.json({ message: "No ad clients found" }, { status: 404 });
        }

        const finduser = await prisma.adminUser.findUnique({
            where: { Id: user.Id.toString() }
        });

        if (!finduser) {
            return NextResponse.json({ error: "User not found." }, { status: 404 });
        }

        if (startDateParams && endDateParams) {
            const finalStartDate = {
                day: startDateParams.split("-")[2],
                month: startDateParams.split("-")[1],
                year: startDateParams.split("-")[0],
            };
            const finalEndDate = {
                day: endDateParams.split("-")[2],
                month: endDateParams.split("-")[1],
                year: endDateParams.split("-")[0],
            };

            const metrics = [
                "ESTIMATED_EARNINGS",
                "IMPRESSIONS",
                "IMPRESSIONS_RPM",
                "CLICKS",
                "IMPRESSIONS_CTR",
                "COST_PER_CLICK",
            ];

            if (["Admin", "Super Admin", "Account", "Partner"].includes(finduser.User_Type || "")) {
                let allReportsData: string[] = [];
                let headerRow = '';

                // Process Channels
                if (selectedChannels.length > 0) {
                    const channelDimensions = await Promise.all(
                        selectedChannels.map(fetchChannelsRecord)
                    );
                    const validChannels = channelDimensions.filter(Boolean);

                    if (validChannels.length > 0) {
                        const channelReport = await generateReport(
                            oauth2Client,
                            exampleAccountId,
                            google,
                            finalStartDate,
                            finalEndDate,
                            metrics,
                            JSON.parse(breakPoints),
                            validChannels,
                            toggle
                        );

                        if (typeof channelReport === 'string') {
                            const [currentHeader, ...rows] = channelReport.split('\n');
                            if (!headerRow) headerRow = currentHeader;
                            for (const row of rows.filter(r => r.trim() !== '')) {
                                allReportsData.push(row);
                            }
                        }
                    }
                }

                // Process Styles
                if (selectedStyles.length > 0) {
                    const styleDimensions = await Promise.all(
                        selectedStyles.map(fetchStyleDimensionId)
                    );
                    const validStyles = styleDimensions.filter(Boolean);

                    if (validStyles.length > 0) {
                        const styleReport = await generateReport(
                            oauth2Client,
                            exampleAccountId,
                            google,
                            finalStartDate,
                            finalEndDate,
                            metrics,
                            JSON.parse(breakPoints),
                            validStyles,
                            toggle
                        );

                        if (typeof styleReport === 'string') {
                            const [currentHeader, ...rows] = styleReport.split('\n');
                            if (!headerRow) headerRow = currentHeader;
                            for (const row of rows.filter(r => r.trim() !== '')) {
                                allReportsData.push(row);
                            }
                        }
                    }
                }

                const combinedReport = [headerRow, ...allReportsData].join('\n');

                if (combinedReport) {
                    const rows = combinedReport.split('\n').slice(1);
                    const batchUpserts = [];

                    for (const row of rows) {
                        if (!row.trim()) continue;

                        const columns = row.split(',');
                        if (columns.length < 13) continue;

                        const [
                            countryName,
                            platformTypeName,
                            date,
                            customChannelName,
                            customChannelId,
                            customSearchStyleName,
                            customSearchStyleId,
                            estimatedEarnings,
                            impressions,
                            impressionsRpm,
                            clicks,
                            impressionsCtr,
                            costPerClick
                        ] = columns;

                        const processValue = (value: string) => value?.trim() || null;
                        const processNumericValue = (value: string) => {
                            const val = processValue(value);
                            return val ? parseFloat(val) : null;
                        };

                        const formattedDate = date ? new Date(date) : null;
                        if (!formattedDate) continue;

                        const finalChannelId = processValue(customChannelId);
                        const finalStyleId = processValue(customSearchStyleId);

                        if (!finalChannelId && !finalStyleId) continue;

                        const revenueData = {
                            Country: processValue(countryName),
                            PlatfromType: processValue(platformTypeName),
                            EstimatedEarnings: processNumericValue(estimatedEarnings),
                            Impressions: processNumericValue(impressions),
                            ImpressionsRpm: processNumericValue(impressionsRpm),
                            Clicks: processNumericValue(clicks),
                            ImpressionsCtr: processNumericValue(impressionsCtr),
                            CostPerClick: processNumericValue(costPerClick)
                        };

                        const uniqueIdentifier = {
                            Date: formattedDate,
                            StyleId: finalStyleId || "",
                            ChannalId: finalChannelId || ""
                        };

                        batchUpserts.push(
                            prisma.revenue.upsert({
                                where: {
                                    Date_StyleId_ChannalId: uniqueIdentifier
                                },
                                create: {
                                    ...uniqueIdentifier,
                                    ...revenueData
                                },
                                update: revenueData
                            })
                        );
                    }

                    if (batchUpserts.length > 0) {
                        await prisma.$transaction(batchUpserts);
                    }
                }

                return new NextResponse(combinedReport, {
                    status: 200,
                    headers: {
                        'Content-Type': 'text/csv',
                        'Content-Disposition': 'attachment; filename="adsense_report.csv"'
                    }
                });
            }
        }

        return NextResponse.json({ message: "Missing date range or unauthorized role" }, { status: 400 });

    } catch (error) {
        console.error("Adsense POST Error:", error);
        return NextResponse.json({ error: "Internal server error" }, { status: 500 });
    } finally {
        await prisma.$disconnect();
    }
}
