import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';


export async function POST(req: NextRequest) {
    try {
        type AuthenticatedUser = {
            Id: string;
        };
        const user = await verifyToken(req) as AuthenticatedUser;
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { Name, Url, Domain, AssignUsers, AccountId, CId, HeadTag, HeadTagScript,
            HeadTagScriptLandingPage, HeadTagScriptSearchPage, GId, AWId, SendTo } = await req.json();

        // Validate required fields
        if (!Name || !Url || !Domain || !AssignUsers) {
            return NextResponse.json(
                { error: 'Name, Url, Domain and AssignUsers are required' },
                { status: 400 }
            );
        }

        if (AccountId && !Array.isArray(AccountId)) {
            return NextResponse.json(
                { error: 'AccountId must be an array' },
                { status: 400 }
            );
        }

        // Validate AssignUsers
        if (AssignUsers !== undefined) {
            if (!Array.isArray(AssignUsers)) {
                return NextResponse.json(
                    { error: 'AssignUsers must be an array' },
                    { status: 400 }
                );
            }
            if (AssignUsers.length > 0) {
                const userIds = AssignUsers;
                const existingUsers = await prisma.adminUser.findMany({
                    where: {
                        Id: {
                            in: userIds,
                        },
                        IsDeleted: false,
                    },
                    select: {
                        Id: true,
                    },
                });

                if (existingUsers.length !== userIds.length) {
                    const foundUserIds = existingUsers.map((user) => user.Id);
                    const invalidUserIds = userIds.filter((id) => !foundUserIds.includes(id));
                    return NextResponse.json(
                        {
                            error: 'One or more user IDs are invalid',
                            invalidUserIds,
                        },
                        { status: 400 }
                    );
                }
            }
        }

        // Check if subdomain already exists
        const existingSubdomain = await prisma.subDomain.findFirst({
            where: { Name },
        });

        if (existingSubdomain) {
            return NextResponse.json(
                { error: 'SubDomain already exists' },
                { status: 409 }
            );
        }

        // Create the subdomain and user mappings in a transaction
        const result = await prisma.$transaction(async (prismaTransaction) => {
            // Create the SubDomain record
            const newSubdomain = await prismaTransaction.subDomain.create({
                data: {
                    Name,
                    Url,
                    Domain,
                    CId,
                    AccountId,
                    HeadTag, HeadTagScript,
                    HeadTagScriptLandingPage,
                    HeadTagScriptSearchPage,
                    GId,
                    AWId,
                    SendTo,
                    CreatedBy: user.Id,
                    CreatedAt: new Date(),
                },
            });

            // Create mappings for each user if provided
            const createdMappings = [];
            if (AssignUsers && AssignUsers.length > 0) {
                for (const userId of AssignUsers) {
                    const mapping = await prismaTransaction.subDomainUserMappings.create({
                        data: {
                            SubDomainId: newSubdomain.Id,
                            UserId: userId,
                        },
                    });
                    createdMappings.push(mapping);
                }
            }

            return {
                subdomain: newSubdomain,
                mappings: createdMappings,
            };
        });

        return NextResponse.json(
            {
                success: true,
                message: 'SubDomain created successfully',
                subdomain: result.subdomain,
                mappingsCreated: result.mappings.length,
            },
            { status: 201 }
        );
    } catch (error) {
        console.error('Error processing request:', error);
        return NextResponse.json(
            {
                error: 'Internal Server Error',
                details: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined,
            },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}