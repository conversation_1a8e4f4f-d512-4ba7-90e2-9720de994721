
"use client";
import React, { useCallback, useRef, useState } from "react";
import axios from "axios";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import CustomDataTable from "@/components/DataTable/CustomDataTable";
import Swal from "sweetalert2";
import { Button } from "@/components/ui-elements/button";

const AllCustomChannels = () => {
  const [tableKey, setTableKey] = useState(0);
  const [showLoader, setShowLoader] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [order, setOrder] = useState("desc");
  const [orderBy, setOrderBy] = useState("DisplayName"); // Fixed initial orderBy
  const [data, setData] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const dataTableRef = useRef();

  const columns = [
    { id: "DisplayName", label: "Channel Name" },
    { id: "Name", label: "Name" },
    { id: "CustomChannelId", label: "Channel ID" },
  ];

  const fetchChannels = useCallback(async () => {
    try {
      setShowLoader(true);
      const response = await axios.get("/api/Channals/Get", { // Fixed typo
        params: {
          q: searchTerm,
          page: page + 1,
          length: rowsPerPage,
          orderBy: orderBy,
          orderDir: order,
        },
        withCredentials: true,
      });

      console.log("API Response:", response.data); // Debug log
      setData(response.data.data || []);
      setTotalCount(response.data.pagination?.recordsFiltered || 0);
    } catch (error) {
      console.error("Error fetching channels:", error); // Debug log
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to load channels",
        timer: 3000,
        showConfirmButton: false,
      });
      setData([]);
      setTotalCount(0);
    } finally {
      setShowLoader(false);
    }
  }, [searchTerm, page, rowsPerPage, order, orderBy]);

  const handleRefreshData = async () => {
    try {
      setShowLoader(true);
      const syncResponse = await axios.get("/api/Channals/GetCustomChannals", { // Fixed typo
        params: {
          customChannels: "true",
        },
        withCredentials: true,
      });

      await fetchChannels();

      await Swal.fire({
        icon: "success",
        title: "Success",
        text: syncResponse.data.message || "Channels synced successfully",
        timer: 3000,
        showConfirmButton: false,
      });
    } catch (error) {
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to refresh custom channels",
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setShowLoader(false);
    }
  };

  const handleRequestSort = (event, property) => {
    const isAsc = orderBy === property && order === "asc";
    setOrder(isAsc ? "desc" : "asc");
    setOrderBy(property);
  };

  const handleChangePage = (newPage) => {
    setPage(newPage);
  };

  const handleSearchChange = (newSearchTerm) => {
    setSearchTerm(newSearchTerm);
    setPage(0);
  };

  React.useEffect(() => {
    fetchChannels();
  }, [fetchChannels]);

  return (
    <>
      <div className="rounded-[10px] border border-stroke bg-white p-4 shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card sm:p-6">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <h2 className="text-2xl font-bold text-dark dark:text-white">
            All Custom Channels
          </h2>
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
            <Button
              type="button"
              label="Get Custom Channels"
              variant="primary"
              shape="rounded"
              onClick={handleRefreshData}
            />
          </div>
        </div>
        <CustomDataTable
          isLoading={showLoader}
          key={tableKey}
          ref={dataTableRef}
          columns={columns}
          rows={data}
          searchTerm={searchTerm}
          onSearchChange={handleSearchChange}
          page={page}
          rowsPerPage={rowsPerPage}
          onPageChange={handleChangePage}
          onRowsPerPageChange={setRowsPerPage}
          totalCount={totalCount}
          order={order}
          isAction={true}
          orderBy={orderBy}
          onRequestSort={handleRequestSort}
        />
      </div>
    </>
  );
};

export default AllCustomChannels;