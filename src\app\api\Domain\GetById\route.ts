import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';


export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        const Id = searchParams.get("id");

        // Build filter
        let where: any = {
            IsDeleted: false,
            Id: Id
        };


        // Fetch data
        const DomainWithUsers = await prisma.domain.findMany({
            where,
            select: {
                Id: true,
                Name: true,
                Prefix: true,
                ShowUrlName: true,
                ChannelId: true,
                CookieMinutes: true,
                StyleIdDm: true,
                StyleIdLm: true,
                HeadTagScript: true,
                HeadTagScriptLandingPage: true,
                HeadTagScriptSearchPage: true,
                GId: true,
                AWId: true,
                SendTo: true
            }
        });

        const formattedData = DomainWithUsers.map(domain => ({
            ...domain,
            StyleIdDm: domain.StyleIdDm !== null ? domain.StyleIdDm.toString() : null,
            StyleIdLm: domain.StyleIdLm !== null ? domain.StyleIdLm.toString() : null
        }));

        return NextResponse.json({
            success: true,
            data: formattedData,
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Internal Server Error", details: error instanceof Error ? error.message : String(error) },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}