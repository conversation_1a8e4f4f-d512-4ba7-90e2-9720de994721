import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';


export async function DELETE(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // const url = new URL(req.url);
        // const userId = url.searchParams.get('Id');
        // const Id = userId;

        const requestData = await req.json();
        const Id = requestData.Id?.trim();
        if (!Id) {
            return NextResponse.json(
                { error: "User Id is required" },
                { status: 400 }
            );
        }

        // Check if the user exists
        const existingUser = await prisma.adminUser.findUnique({
            where: {
                Id: Id
            }
        });

        if (!existingUser) {
            return NextResponse.json(
                { error: "User not found" },
                { status: 404 }
            );
        }

        // Delete the user
        // await prisma.AdminUser.delete({
        //     where: {
        //         Id: Id
        //     }
        // });
        const updateData: any = {};
        updateData.IsDeleted = true;
        const updatedUser = await prisma.adminUser.update({
            where: {
                Id: Id
            },
            data: updateData
        });

        return NextResponse.json(
            {
                success: true,
                message: "User deleted successfully"
            },
            { status: 200 }
        );
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}