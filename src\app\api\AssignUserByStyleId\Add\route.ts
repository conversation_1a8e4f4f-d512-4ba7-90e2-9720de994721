import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function POST(req: NextRequest) {
    try {
        type AuthenticatedUser = {
            Id: string;
        };

        const user = await verifyToken(req) as AuthenticatedUser;
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { StyleId, AssignUser } = await req.json();

        // Validate StyleId is an array
        if (!StyleId || !Array.isArray(StyleId) || StyleId.length === 0) {
            return NextResponse.json(
                { error: "StyleId must be a non-empty array" },
                { status: 400 }
            );
        }

        if (!AssignUser) {
            return NextResponse.json(
                { error: "AssignUsers is required" },
                { status: 400 }
            );
        }

        // Validate user ID exists
        const existingUser = await prisma.adminUser.findFirst({
            where: {
                Id: AssignUser,
                IsDeleted: false
            },
            select: {
                Id: true
            }
        });

        if (!existingUser) {
            return NextResponse.json(
                { error: "User ID is invalid" },
                { status: 400 }
            );
        }

        // Check for existing mappings
        const existingMappings = await prisma.styleIdUserMappings.findMany({
            where: {
                StyleId: {
                    in: StyleId
                },
                UserId: AssignUser
            },
            select: {
                StyleId: true
            }
        });

        const existingStyleIds = existingMappings.map(mapping => mapping.StyleId);
        const newStyleIds = StyleId.filter(styleId => !existingStyleIds.includes(styleId));

        // If all mappings already exist
        if (newStyleIds.length === 0) {
            return NextResponse.json(
                {
                    success: false,
                    message: "All StyleId-UserId mappings already exist",
                },
                { status: 409 }
            );
        }

        // Create mappings for StyleIds that don't already exist
        const createdMappings = [];
        for (const styleId of newStyleIds) {
            const mapping = await prisma.styleIdUserMappings.create({
                data: {
                    StyleId: styleId,
                    UserId: AssignUser
                }
            });
            createdMappings.push(mapping);
        }

        return NextResponse.json(
            {
                success: true,
                message: "User mappings created successfully",
                NewMappings: createdMappings.length,
                existingMappings: existingStyleIds.length
            },
            { status: 201 }
        );
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}