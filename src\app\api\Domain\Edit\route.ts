import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';


export async function PUT(req: NextRequest) {
    try {
        type AuthenticatedUser = {
            Id: string;
        };
        const user = await verifyToken(req) as AuthenticatedUser;
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { Id, Name, Prefix, ShowUrlName, ChannelId, CookieMinutes, StyleIdDm, StyleIdLm, HeadTagScript,
            HeadTagScriptLandingPage, HeadTagScriptSearchPage, GId, AWId, SendTo } = await req.json();

        if (!Id) {
            return NextResponse.json(
                { error: "Id is required for updating a Domain" },
                { status: 400 }
            );
        }

        if (!Name || !ShowUrlName) {
            return NextResponse.json(
                { error: "ShowUrlName and Name are required" },
                { status: 400 }
            );
        }

        // Check if the domain exists and belongs to the user
        const existingDomain = await prisma.domain.findFirst({
            where: {
                Id: Id,
                // CreatedBy: user.Id
            }
        });

        if (!existingDomain) {
            return NextResponse.json(
                { error: "Domain not found or you don't have permission to update it" },
                { status: 404 }
            );
        }

        const nameConflict = await prisma.domain.findFirst({
            where: {
                Name,
                IsDeleted: false,
                NOT: {
                    Id: Id
                }
            }
        });

        if (nameConflict) {
            return NextResponse.json(
                { error: "Name already exists" },
                { status: 409 }
            );
        }

        const updateDomain = await prisma.domain.update({
            where: { Id },
            data: {
                Name,
                Prefix,
                ShowUrlName,
                ChannelId,
                CookieMinutes,
                StyleIdDm,
                StyleIdLm,
                HeadTagScript,
                HeadTagScriptLandingPage,
                HeadTagScriptSearchPage,
                GId,
                AWId,
                SendTo,
                UpdatedAt: new Date()
            }
        });

        return NextResponse.json(
            {
                success: true,
                message: "Domain updated successfully",
                data: updateDomain
            },
            { status: 200 }
        );
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}