import { prisma } from '@/lib/prisma';
import { NextRequest, NextResponse } from 'next/server';


export async function POST(req: NextRequest) {
  try {

    return NextResponse.json({
      success: true,
    });
  } catch (err) {
    console.error("Login error:", err);
    return NextResponse.json({ error: 'Server error' }, { status: 500 });
  } finally {
        await prisma.$disconnect();
    }
}
