import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);

        // Parse DataTables-style parameters
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const search = searchParams.get("q");
        const orderBy = searchParams.get("orderBy") || "CreatedAt";
        const orderDir = searchParams.get("orderDir") || "desc";

        let skip: number | undefined;
        let limit: number | undefined;

        if (length == -1) {
            skip = undefined;
            limit = undefined;
        }
        else {
            const skipCount = (start == 1 ? 0 : start - 1) * length;
            limit = length;
            skip = (skipCount > 0 ? skipCount : 0);
        }
        let where: any = {
            IsDeleted: false
        };

        if (search) {
            where.Name = {
                contains: search,
                mode: 'insensitive'
            };
        }

        const orderByClause = {
            [orderBy]: orderDir.toLowerCase() as 'asc' | 'desc',
        };

        // Fetch paginated data
        const categories = await prisma.category.findMany({
            where,
            select: {
                Id: true,
                Name: true,
                ShowUrlName: true
            },
            skip,
            take: limit,
            orderBy: orderByClause
        });

        const recordsTotal = await prisma.category.count({
            where: {
                IsDeleted: false
            }
        });

        const recordsFiltered = await prisma.category.count({ where });
        const totalPages = length === -1 ? 1 : Math.ceil(recordsFiltered / (length || 1));
        // Format response to match what the Category component expects
        return NextResponse.json({
            success: true,
            data: categories,
            pagination: {
                draw: parseInt(searchParams.get("draw") || "1"),
                //recordsTotal,
                recordsFiltered,
                currentPageCount: categories.length,
                start,
                length,
                currentPage: start,
                totalPages: totalPages,
                hasNextPage: length === -1 ? false : start * length < recordsFiltered,
                hasPreviousPage: start > 1,
            }
        });

    } catch (error) {
        console.error('Error fetching categories:', error);
        return NextResponse.json({ error: 'Category Not Found' }, { status: 500 });
    } finally {
        await prisma.$disconnect(); // Ensure Prisma client disconnects
    }
}
