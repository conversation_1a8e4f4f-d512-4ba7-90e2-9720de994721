import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import { alertTitleClasses } from '@mui/material';

export async function GET(req: NextRequest) {
  try {
    type AuthenticatedUser = {
      Id: string;
      User_Type: string;
    };
    const user = (await verifyToken(req)) as AuthenticatedUser;
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const start = parseInt(searchParams.get('page') || '1');
    const length = parseInt(searchParams.get('length') || '10');
    const search = searchParams.get('q');
    const orderByParam = searchParams.get('orderBy') || 'CreatedAt';
    const orderDirParam = (searchParams.get('orderDir') || 'asc').toLowerCase();
    const categoryIdFilter = searchParams.get('CategoryId') || '';
    const userIdFilter = searchParams.get('userId');

    const validOrderFields = ['CreatedAt', 'Title', 'UpdatedAt', 'CustomChannalId', 'CategoryName', 'ChannelName', 'DomainName', 'SubDomainName', 'CampaignCount', 'UserName', 'ShowsAds', 'Published'];
    const orderBy = validOrderFields.includes(orderByParam) ? orderByParam : 'CreatedAt';
    const orderDir = orderDirParam === 'desc' ? 'desc' : 'asc';

    let skip: number | undefined;
    let limit: number | undefined;

    if (length == -1) {
      skip = undefined;
      limit = undefined;
    }
    else {
      const skipCount = (start == 1 ? 0 : start - 1) * length;
      limit = length;
      skip = (skipCount > 0 ? skipCount : 0);
    }

    let where: any = {
      IsDeleted: false,
    };

    if (user.User_Type.toLowerCase() !== 'super admin') {
      where.User_Id_Settings = user.Id;
    } else if (userIdFilter) {
      where.User_Id_Settings = userIdFilter;
    }

    if (categoryIdFilter) {
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(categoryIdFilter)) {
        return NextResponse.json({ error: 'Invalid category ID format' }, { status: 400 });
      }
      where.Category = categoryIdFilter;
    }

    // Count totals
    const recordsTotal = await prisma.articleDetails.count({
      where: { IsDeleted: false },
    });

    // First fetch all related data for search functionality
    const allArticles = await prisma.articleDetails.findMany({
      where: {
        IsDeleted: false,
        ...(user.User_Type.toLowerCase() !== 'super admin' ? { User_Id_Settings: user.Id } : {}),
        ...(userIdFilter ? { User_Id_Settings: userIdFilter } : {}),
        ...(categoryIdFilter ? { Category: categoryIdFilter } : {}),
      },
      select: {
        Id: true,
        Title: true,
        Category: true,
        CustomChannal: true,
        Domain: true,
        SubDomain: true,
        ShowsAds: true,
        Published: true,
        User_Id_Settings: true,
        Url: true,
        CreatedAt: true,
        _count: {
          select: {
            ArticleCampaignMappings: true,
          },
        },
      },
    });

    // Collect IDs for related data
    const categoryIds = [...new Set(allArticles.map((article) => article.Category).filter(Boolean))];
    const channelIds = [...new Set(allArticles.map((article) => article.CustomChannal).filter(Boolean))];
    const domainIds = [...new Set(allArticles.map((article) => article.Domain).filter(Boolean))];
    const adminUserIds = [...new Set(allArticles.map((article) => article.User_Id_Settings).filter(Boolean))];
    const subDomainIds = [...new Set(allArticles.map((article) => article.SubDomain).filter(Boolean))];

    const filteredSubDomainIds = subDomainIds.filter((id): id is string => id !== null);
    const filteredCategoryIds = categoryIds.filter((id): id is string => id !== null);
    const filteredChannelIds = channelIds.filter((id): id is string => id !== null);
    const filteredDomainIds = domainIds.filter((id): id is string => id !== null);

    const categories =
      filteredCategoryIds.length > 0
        ? await prisma.category.findMany({
          where: { Id: { in: filteredCategoryIds } },
          select: { Id: true, Name: true },
        })
        : [];

    const channels =
      filteredChannelIds.length > 0
        ? await prisma.channals.findMany({
          where: { Id: { in: filteredChannelIds } },
          select: { Id: true, Name: true, DisplayName: true },
        })
        : [];
    const domains =
      filteredDomainIds.length > 0
        ? await prisma.domain.findMany({
          where: { Id: { in: filteredDomainIds } },
          select: { Id: true, Name: true },
        })
        : [];
    const adminUsers =
      adminUserIds.length > 0
        ? await prisma.adminUser.findMany({
          where: { Id: { in: adminUserIds as string[] } },
          select: { Id: true, Name: true },
        })
        : [];
    const subDomains =
      filteredSubDomainIds.length > 0
        ? await prisma.subDomain.findMany({
          where: { Id: { in: filteredSubDomainIds } },
          select: { Id: true, Name: true },
        })
        : [];

    const categoryMap = new Map(categories.map((cat) => [cat.Id, cat.Name]));
    const channelMap = new Map(channels.map((ch) => [ch.Id, { Name: ch.Name, DisplayName: ch.DisplayName }]));
    const domainMap = new Map(domains.map((domain) => [domain.Id, domain.Name]));
    const adminUserMap = new Map(adminUsers.map((adminUser) => [adminUser.Id, adminUser.Name]));
    const subDomainMap = new Map(subDomains.map((subDomain) => [subDomain.Id, subDomain.Name]));

    const extractCustomChannalId = (channelName: string | null | undefined): string | null => {
      if (!channelName) return null;
      const parts = channelName.split('/');
      return parts.length > 0 ? parts[parts.length - 1] : null;
    };

    // Transform all articles first for searching and sorting
    let transformedArticles = allArticles.map((article) => {
      const channelData = article.CustomChannal ? channelMap.get(article.CustomChannal) : null;
      const channelName = channelData?.DisplayName || channelData?.Name || null;

      return {
        Id: article.Id,
        Title: article.Title,
        Url: article.Url,
        CustomChannalId: extractCustomChannalId(channelData?.Name),
        CategoryName: article.Category ? categoryMap.get(article.Category) || null : null,
        ChannelName: channelName,
        DomainName: article.Domain ? domainMap.get(article.Domain) || null : null,
        ShowsAds: article.ShowsAds,
        Published: article.Published,
        UserName: article.User_Id_Settings ? adminUserMap.get(article.User_Id_Settings) || null : null,
        CampaignCount: article._count.ArticleCampaignMappings,
        SubDomainName: article.SubDomain ? subDomainMap.get(article.SubDomain) || null : null,
        CreatedAt: article.CreatedAt,
      };
    });

    // Apply search filter if search term exists
    if (search) {
      const searchTerm = search.toLowerCase();
      transformedArticles = transformedArticles.filter(article => {
        return (
          (article.Title?.toLowerCase().includes(searchTerm)) ||
          (article.CustomChannalId?.toLowerCase().includes(searchTerm)) ||
          (article.CategoryName?.toLowerCase().includes(searchTerm)) ||
          (article.ChannelName?.toLowerCase().includes(searchTerm)) ||
          (article.DomainName?.toLowerCase().includes(searchTerm)) ||
          (article.SubDomainName?.toLowerCase().includes(searchTerm)) ||
          (article.UserName?.toLowerCase().includes(searchTerm)) ||
          (article.ShowsAds?.toString().toLowerCase().includes(searchTerm)) ||
          (article.Published?.toString().toLowerCase().includes(searchTerm)) ||
          (article.CampaignCount?.toString().includes(searchTerm))
        );
      });
    }

    // Apply sorting
    if (orderBy === 'Title') {
      transformedArticles.sort((a, b) => {
        const valA = a.Title?.toLowerCase() || '';
        const valB = b.Title?.toLowerCase() || '';
        return orderDir === 'desc' ? valB.localeCompare(valA) : valA.localeCompare(valB);
      });
    } else if (orderBy === 'CustomChannalId') {
      transformedArticles.sort((a, b) => {
        const valA = a.CustomChannalId?.toLowerCase() || '';
        const valB = b.CustomChannalId?.toLowerCase() || '';
        return orderDir === 'desc' ? valB.localeCompare(valA) : valA.localeCompare(valB);
      });
    } else if (orderBy === 'CategoryName') {
      transformedArticles.sort((a, b) => {
        const valA = a.CategoryName?.toLowerCase() || '';
        const valB = b.CategoryName?.toLowerCase() || '';
        return orderDir === 'desc' ? valB.localeCompare(valA) : valA.localeCompare(valB);
      });
    } else if (orderBy === 'ChannelName') {
      transformedArticles.sort((a, b) => {
        const valA = a.ChannelName?.toLowerCase() || '';
        const valB = b.ChannelName?.toLowerCase() || '';
        return orderDir === 'desc' ? valB.localeCompare(valA) : valA.localeCompare(valB);
      });
    } else if (orderBy === 'DomainName') {
      transformedArticles.sort((a, b) => {
        const valA = a.DomainName?.toLowerCase() || '';
        const valB = b.DomainName?.toLowerCase() || '';
        return orderDir === 'desc' ? valB.localeCompare(valA) : valA.localeCompare(valB);
      });
    } else if (orderBy === 'SubDomainName') {
      transformedArticles.sort((a, b) => {
        const valA = a.SubDomainName?.toLowerCase() || '';
        const valB = b.SubDomainName?.toLowerCase() || '';
        return orderDir === 'desc' ? valB.localeCompare(valA) : valA.localeCompare(valB);
      });
    } else if (orderBy === 'UserName') {
      transformedArticles.sort((a, b) => {
        const valA = a.UserName?.toLowerCase() || '';
        const valB = b.UserName?.toLowerCase() || '';
        return orderDir === 'desc' ? valB.localeCompare(valA) : valA.localeCompare(valB);
      });
    } else if (orderBy === 'ShowsAds') {
      transformedArticles.sort((a, b) => {
        const valA = a.ShowsAds ? 'true' : 'false';
        const valB = b.ShowsAds ? 'true' : 'false';
        return orderDir === 'desc' ? valB.localeCompare(valA) : valA.localeCompare(valB);
      });
    } else if (orderBy === 'Published') {
      transformedArticles.sort((a, b) => {
        const valA = a.Published ? 'true' : 'false';
        const valB = b.Published ? 'true' : 'false';
        return orderDir === 'desc' ? valB.localeCompare(valA) : valA.localeCompare(valB);
      });
    } else if (orderBy === 'CampaignCount') {
      transformedArticles.sort((a, b) => {
        return orderDir === 'desc' ? b.CampaignCount - a.CampaignCount : a.CampaignCount - b.CampaignCount;
      });
    } else if (orderBy === 'CreatedAt') {
      transformedArticles.sort((a, b) => {
        const valA = a.CreatedAt?.getTime() || 0;
        const valB = b.CreatedAt?.getTime() || 0;
        return orderDir === 'desc' ? valB - valA : valA - valB;
      });
    }

    // Apply pagination after sorting and filtering
    const recordsFiltered = transformedArticles.length;
    const paginatedArticles = length === -1
      ? transformedArticles
      : transformedArticles.slice(skip || 0, (skip || 0) + (limit || transformedArticles.length));

    const totalPages = length === -1 ? 1 : Math.ceil(recordsFiltered / (length || 1));

    return NextResponse.json({
      success: true,
      data: paginatedArticles,
      pagination: {
        draw: parseInt(searchParams.get('draw') || '1'),
        recordsFiltered,
        currentPageCount: paginatedArticles.length,
        start,
        length,
        currentPage: start,
        totalPages: totalPages,
        hasNextPage: length === -1 ? false : start * length < recordsFiltered,
        hasPreviousPage: start > 1,
      },
    });
  } catch (error) {
    console.error('Error fetching articles:', error);
    return NextResponse.json({ error: 'Failed to fetch articles' }, { status: 500 });
  } finally {
    await prisma.$disconnect();
  }
}