"use client";

import { UserIcon } from "@/assets/icons";
import InputGroup from "@/components/FormElements/InputGroup";
import { TextAreaGroup } from "@/components/FormElements/InputGroup/text-area";
import { Select } from "@/components/FormElements/select";
import { ShowcaseSection } from "@/components/Layouts/showcase-section";
import { Button } from "@/components/ui-elements/button";
import { Box, Dialog, DialogActions, DialogContent, DialogTitle, IconButton, Modal, Typography } from "@mui/material";
import axios from "axios";
import { useState, useEffect } from "react";
import Swal from "sweetalert2";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";
import { IoCloseOutline } from "react-icons/io5";

export default function AdminSettings() {
  const [settings, setSettings] = useState({
    LmStyleId: "",
    DmStyleId: "",
    ChannalId: "",
    PubId: "",
    AdsAccountId: "",
    AdsClientId: "",
    HeadTagJSON: "{}",
    CountAdsClick: "",
  });
  const [channels, setChannels] = useState({
    channelsList: [],
    isLoading: false,
  });
  const [passwordData, setPasswordData] = useState({
    password: "",
    newpassword: "",
    reenternewpassword: "",
  });
  const [passwordErrors, setPasswordErrors] = useState({
    newpassword: "",
    reenternewpassword: "",
  });
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  interface ChannelItem {
    Id: string;
    DisplayName: string;
  }

  useEffect(() => {
    const fetchSettings = async () => {
      setIsLoading(true);
      try {
        const response = await fetch("/api/AdminSetting/Get", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
        });
        const data = await response.json();
        if (data.success && data.data) {
          setSettings({
            LmStyleId: data.data.LmStyleId || "",
            DmStyleId: data.data.DmStyleId || "",
            ChannalId: data.data.ChannalId || "",
            PubId: data.data.PubId || "",
            AdsAccountId: data.data.AdsAccountId || "",
            AdsClientId: data.data.AdsClientId || "",
            HeadTagJSON: data.data.HeadTagJSON || "{}",
            CountAdsClick: data.data.CountAdsClick || "",
          });
        } else {
          Swal.fire({
            icon: "error",
            title: "Error",
            text: data.error || "Failed to load settings",
          });
        }
      } catch (error) {
        Swal.fire({
          icon: "error",
          title: "Error",
          text: "Failed to load settings",
        });
      } finally {
        setIsLoading(false);
      }
    };
    const fetchChannels = async () => {
      try {
        setChannels((prev) => ({ ...prev, isLoading: true }));

        const response = await axios.get("/api/Channals/GetDropdown", {
          withCredentials: true,
        });

        if (response.data.success) {
          setChannels({
            channelsList: response.data.data,
            isLoading: false,
          });
        } else {
          console.error("Failed to fetch channels:", response.data.error);
          setChannels((prev) => ({ ...prev, isLoading: false }));
        }
      } catch (error) {
        console.error("Error fetching channels:", error);
        setChannels((prev) => ({ ...prev, isLoading: false }));
      }
    };

    fetchSettings();
    fetchChannels();
  }, []);

  const handleSettingsChange = (
    e:
      | React.ChangeEvent<
        HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
      >
      | { target: { name: string; value: string } },
  ) => {
    const { name, value } = e.target;
    setSettings((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSettingsSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      const response = await fetch("/api/AdminSetting/Get", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(settings),
      });
      const data = await response.json();
      if (data.success) {
        await Swal.fire({
          icon: "success",
          title: "Success",
          text: data.message || "Settings updated successfully",
          timer: 2000,
          showConfirmButton: false,
        });
      } else {
        throw new Error(data.error || "Failed to update settings");
      }
    } catch (error: any) {
      Swal.fire({
        icon: "error",
        title: "Error",
        text: error.message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordData((prev) => ({ ...prev, [name]: value }));

    if (name === "newpassword") {
      if (
        passwordData.reenternewpassword &&
        value !== passwordData.reenternewpassword
      ) {
        setPasswordErrors((prev) => ({
          ...prev,
          reenternewpassword: "Passwords do not match",
        }));
      } else if (passwordData.reenternewpassword) {
        setPasswordErrors((prev) => ({
          ...prev,
          reenternewpassword: "",
        }));
      }
    }

    if (name === "reenternewpassword") {
      if (value !== passwordData.newpassword) {
        setPasswordErrors((prev) => ({
          ...prev,
          reenternewpassword: "Passwords do not match",
        }));
      } else {
        setPasswordErrors((prev) => ({
          ...prev,
          reenternewpassword: "",
        }));
      }
    }
  };

  const validatePasswordForm = () => {
    let isValid = true;
    const newErrors = {
      newpassword: "",
      reenternewpassword: "",
    };

    if (!passwordData.password) {
      setIsModalOpen(false);
      Swal.fire({
        icon: "error",
        title: "Error",
        text: "Current password is required",
      });
      return false;
    }

    if (!passwordData.newpassword) {
      newErrors.newpassword = "New password is required";
      isValid = false;
    }

    if (!passwordData.reenternewpassword) {
      newErrors.reenternewpassword = "Please confirm your new password";
      isValid = false;
    } else if (passwordData.newpassword !== passwordData.reenternewpassword) {
      newErrors.reenternewpassword = "Passwords do not match";
      isValid = false;
    }

    setPasswordErrors(newErrors);

    if (!isValid) {
      return false;
    }

    return true;
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validatePasswordForm()) return;

    setIsLoading(true);
    try {
      const response = await fetch("/api/adminuser/ResetPassword", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(passwordData),
      });
      const data = await response.json();
      if (data.success) {
        await Swal.fire({
          icon: "success",
          title: "Success",
          text: data.message || "Password reset successfully",
          timer: 2000,
          showConfirmButton: false,
        });
        setIsModalOpen(false);
        setPasswordData({
          password: "",
          newpassword: "",
          reenternewpassword: "",
        });
        setPasswordErrors({ newpassword: "", reenternewpassword: "" });
      } else {
        throw new Error(data.error || "Failed to reset password");
      }
    } catch (error: any) {
      Swal.fire({
        icon: "error",
        title: "Error",
        text: error.message,
      });
    } finally {
      setIsModalOpen(false);
      setIsLoading(false);
    }
  };
  const styleForModal = {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: { xs: "90%", sm: "70%", md: "50%", lg: "30%" },
    bgcolor: "background.paper",
    border: "none",
    boxShadow: 24,
    p: 4,
    borderRadius: "16px",
    maxHeight: { xs: "90vh", md: "100vh" },
    overflowY: "auto",
  };

  return (
    <div className="rounded-[10px] border border-stroke bg-white p-4 shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card sm:p-6">
      <div className="flex flex-col gap-4 ">
        <h2 className="text-2xl font-bold text-dark dark:text-white ">Setting</h2>
        <form
          className="mx-auto grid w-full grid-cols-1 gap-6 md:grid-cols-2"
          onSubmit={handleSettingsSubmit}
        >
          <InputGroup
            label="LM Style ID"
            type="text"
            name="LmStyleId"
            value={settings.LmStyleId}
            handleChange={handleSettingsChange}
            placeholder="Enter LM Style ID"
            disabled={isLoading}
          />

          <InputGroup
            label="DM Style ID"
            type="text"
            name="DmStyleId"
            value={settings.DmStyleId}
            handleChange={handleSettingsChange}
            placeholder="Enter DM Style ID"
            disabled={isLoading}
          />
          <SearchableDropdown
            label="Channel ID"
            options={channels.channelsList}
            placeholder="Select Channel"
            value={settings.ChannalId}
            onChange={(item: ChannelItem) =>
              handleSettingsChange({
                target: { name: "ChannalId", value: item?.Id },
              })
            }
            displayKey="DisplayName"
            idKey="Id"
          />
          <InputGroup
            label="Pub ID"
            type="text"
            name="PubId"
            value={settings.PubId}
            handleChange={handleSettingsChange}
            placeholder="Enter Pub ID"
            disabled={isLoading}
          />

          <InputGroup
            label="Ads Account ID"
            type="text"
            name="AdsAccountId"
            value={settings.AdsAccountId}
            handleChange={handleSettingsChange}
            placeholder="Enter Ads Account ID"
            disabled={isLoading}
          />

          <InputGroup
            label="Ads Client ID"
            type="text"
            name="AdsClientId"
            value={settings.AdsClientId}
            handleChange={handleSettingsChange}
            placeholder="Enter Ads Client ID"
            disabled={isLoading}
          />

          <TextAreaGroup
            label="Head Tag JSON"
            name="HeadTagJSON"
            value={settings.HeadTagJSON}
            handleChange={handleSettingsChange}
            rows={4}
            className="md:col-span-2"
            disabled={isLoading}
          />

          <InputGroup
            label="Max Count"
            type="text"
            name="CountAdsClick"
            value={settings.CountAdsClick}
            handleChange={handleSettingsChange}
            placeholder="Enter Max Count"
            disabled={isLoading}
          />

          <div className="flex flex-col gap-4 pt-4 sm:flex-row md:col-span-2">
            <Button
              type="submit"
              label={isLoading ? "Processing..." : "Submit"}
              variant="primary"
              shape="rounded"
              className="flex items-center justify-center gap-2"
              disabled={isLoading}
            />

            <Button
              type="reset"
              label="Reset Password"
              variant="dark"
              shape="rounded"
              className="flex items-center justify-center gap-2"
              disabled={isLoading}
              onClick={(e) => {
                e.preventDefault();
                setIsModalOpen(true);
                setPasswordData({
                  password: "",
                  newpassword: "",
                  reenternewpassword: "",
                });
              }}
            />
          </div>
        </form>

      <Dialog
  open={isModalOpen}
  onClose={() => {
    setIsModalOpen(false);
    // Add any reset logic here if needed
  }}
  fullWidth
  maxWidth="sm"
  PaperProps={{
    sx: {
      maxHeight: "90vh",
    },
  }}
>
  <DialogTitle
    sx={{
      display: "flex",
      justifyContent: "space-between",
      alignItems: "center",
      color: "white",
      py: 2,
      px: 3,
    }}
    className="bg-primary text-white"
  >
    <span style={{ fontSize: "1.25rem", fontWeight: 500 }}>
      Reset Password
    </span>
    <IconButton
      aria-label="close"
      onClick={() => {
        setIsModalOpen(false);
        // Add any reset logic here if needed
      }}
      sx={{
        color: "white",
      }}
    >
      <IoCloseOutline size={24} />
    </IconButton>
  </DialogTitle>

  <DialogContent dividers sx={{ py: 3, px: 3 }}>
    <form className="space-y-4">
        <InputGroup
          label="Current Password"
          type="password"
          name="password"
          value={passwordData.password}
          handleChange={handlePasswordChange}
          placeholder="Enter current password"
          disabled={isLoading}
        />

        <div>
          <InputGroup
            label="New Password"
            type="password"
            name="newpassword"
            value={passwordData.newpassword}
            handleChange={handlePasswordChange}
            placeholder="Enter new password"
            disabled={isLoading}
          />
          {passwordErrors.newpassword && (
            <Typography variant="caption" color="error" sx={{ mt: 1 }}>
              {passwordErrors.newpassword}
            </Typography>
          )}
        </div>

        <div>
          <InputGroup
            label="Confirm New Password"
            type="password"
            name="reenternewpassword"
            value={passwordData.reenternewpassword}
            handleChange={handlePasswordChange}
            placeholder="Re-enter new password"
            disabled={isLoading}
          />
          {passwordErrors.reenternewpassword && (
            <Typography variant="caption" color="error" sx={{ mt: 1 }}>
              {passwordErrors.reenternewpassword}
            </Typography>
          )}
        </div>
    </form>
  </DialogContent>

  <DialogActions sx={{ py: 2, px: 3 }}>
    <Button
      type="submit"
      label={isLoading ? "Processing..." : "Reset Password"}
      variant="primary"
      shape="rounded"
      onClick={handlePasswordSubmit}
      disabled={isLoading}
    />
  </DialogActions>
</Dialog>
      </div>
    </div>
  );
}
