'use client';
import { useEffect, useRef, useState } from 'react';
import { GoTriangleUp, GoTriangleDown } from 'react-icons/go';
 
const MultiSelectDropdown = ({
  label,
  options = [],
  placeholder = 'Search...',
  value = [],
  onChange,
  error = '',
  displayKey = 'DisplayName',
  idKey = 'Id',
  showSelectAll = true,
}) => {
  const dropdownRef = useRef(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  
  const filteredOptions = options.filter(
    (item) =>
      item[displayKey]?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item[idKey]?.toLowerCase().includes(searchTerm.toLowerCase())
  );
 
  // Check if all filtered options are selected
  const allFilteredSelected = filteredOptions.length > 0 &&
    filteredOptions.every(item => value.includes(item[idKey]));
 
  const handleToggle = (itemId) => {
    const newValue = value.includes(itemId)
      ? value.filter(id => id !== itemId)
      : [...value, itemId];
    onChange(newValue);
  };
 
  const handleSelectAll = () => {
    if (allFilteredSelected) {
      // Deselect all filtered options
      const filteredIds = filteredOptions.map(item => item[idKey]);
      const newValue = value.filter(id => !filteredIds.includes(id));
      onChange(newValue);
    } else {
      // Select all filtered options
      const filteredIds = filteredOptions.map(item => item[idKey]);
      const existingIds = new Set(value);
      const newValue = [...value];
      
      filteredIds.forEach(id => {
        if (!existingIds.has(id)) {
          newValue.push(id);
        }
      });
      
      onChange(newValue);
    }
  };
 
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);
 
  return (
    <div className="w-full mb-3">
      <label className="mb-4 block text-sm font-bold text-dark dark:text-white">
        {label}
      </label>
      <div className="relative" ref={dropdownRef}>
        <button
          type="button"
          className="flex w-full items-center justify-between rounded-lg  border-[1.5px] border-stroke bg-transparent px-5 py-3 text-left outline-none transition-all duration-200 focus:border-primary dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary"
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        >
          <span className="text-gray-500 dark:text-gray-400">
            {value.length === 0
              ? placeholder
              : `${value.length} ${value.length === 1 ? "item" : "items"} selected`}
          </span>
          {isDropdownOpen ? <GoTriangleUp /> : <GoTriangleDown />}
        </button>
 
        {isDropdownOpen && (
          <div className="absolute z-10 mt-1 w-full rounded-lg border border-stroke bg-white shadow-lg dark:border-dark-3 dark:bg-gray-dark">
            <div className="p-2">
              <input
                type="text"
                className="w-full rounded-lg  border-[1.5px] border-stroke bg-transparent px-3 py-2 text-sm outline-none focus:border-primary dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary"
                placeholder="Search..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="max-h-50 overflow-y-auto">
              {showSelectAll && (
                <div className="px-3 py-1">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={allFilteredSelected}
                      onChange={handleSelectAll}
                      className="rounded border-stroke text-primary focus:ring-primary dark:border-dark-3"
                    />
                    <span>Select All</span>
                  </label>
                </div>
              )}
              {filteredOptions.length > 0 ? (
                filteredOptions.map((item) => (
                  <div
                    key={item[idKey]}
                    className="px-3 py-1 hover:bg-gray-100 dark:hover:bg-dark-3"
                  >
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={value.includes(item[idKey])}
                        onChange={() => handleToggle(item[idKey])}
                        className="rounded border-stroke text-primary focus:ring-primary dark:border-dark-3"
                      />
                      <span>{item[displayKey]}</span>
                    </label>
                  </div>
                ))
              ) : (
                <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                  No options found
                </div>
              )}
            </div>
          </div>
        )}
      </div>
      {error && <p className="text-red-500 mt-1 text-sm">{error}</p>}
    </div>
  );
};
 
export default MultiSelectDropdown; 