import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';
import { Prisma } from '@prisma/client';

// const UPLOAD_DIR = path.join(process.cwd(), 'public/uploads');
const UPLOAD_DIR = process.env.IMAGE_STORAGE_PATH || "/var/www/images";

export async function PUT(req: NextRequest) {
    type AuthenticatedUser = {
        Id: string;
    };
    const user = await verifyToken(req) as AuthenticatedUser;
    if (!user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    try {
        const formData = await req.formData();

        // Extract all form fields
        const formFields = {
            Id: formData.get('Id'),
            Title: formData.get('Title'),
            Category: formData.get('Category'),
            Url: formData.get('Url'),
            Description: formData.get('Description'),
            Published: formData.get('Published'),
            ShowArticle: formData.get('ShowArticle'),
            ShowsAds: formData.get('ShowsAds'),
            MetaTitle: formData.get('MetaTitle'),
            MetaDescription: formData.get('MetaDescription'),
            MetaKeys: formData.get('MetaKeys'),
            CustomChannal: formData.get('CustomChannal'),
            StyleId: formData.get('StyleId'),
            AdRelatedSearches: formData.get('AdRelatedSearches'),
            Remark: formData.get('Remark'),
            ShortDescription: formData.get('ShortDescription'),
            Domain: formData.get('Domain'),
            SubDomain: formData.get('SubDomain'),
            CampaignIds: formData.get('CampaignIds'),
            file: formData.get('file')
        };

        // Validate required ID
        if (!formFields.Id?.toString().trim()) {
            return NextResponse.json(
                { error: "Article ID is required" },
                { status: 400 }
            );
        }

        // Check if article exists
        const existingArticle = await prisma.articleDetails.findUnique({
            where: { Id: formFields.Id.toString() }
        });

        if (!existingArticle) {
            return NextResponse.json(
                { error: "Article not found" },
                { status: 404 }
            );
        }

        // Type cast only fields that have values
        const textFields = {
            Id: formFields.Id.toString(),
            Title: formFields.Title !== null ? formFields.Title.toString() : undefined,
            Category: formFields.Category !== null ? formFields.Category.toString() : undefined,
            Url: formFields.Url !== null ? formFields.Url.toString() : undefined,
            Description: formFields.Description !== null ? formFields.Description.toString() : undefined,
            Published: formFields.Published !== null ? formFields.Published === 'true' : undefined,
            ShowArticle: formFields.ShowArticle !== null ? formFields.ShowArticle === 'true' : undefined,
            ShowsAds: formFields.ShowsAds !== null ? formFields.ShowsAds === 'true' : undefined,
            MetaTitle: formFields.MetaTitle !== null ? formFields.MetaTitle.toString() : undefined,
            MetaDescription: formFields.MetaDescription !== null ? formFields.MetaDescription.toString() : undefined,
            MetaKeys: formFields.MetaKeys !== null ? formFields.MetaKeys.toString() : undefined,
            CustomChannal: formFields.CustomChannal !== null ? formFields.CustomChannal.toString() : undefined,
            StyleId: formFields.StyleId !== null ? formFields.StyleId.toString() : undefined,
            AdRelatedSearches: formFields.AdRelatedSearches !== null ? formFields.AdRelatedSearches.toString() : undefined,
            Remark: formFields.Remark !== null ? formFields.Remark.toString() : undefined,
            ShortDescription: formFields.ShortDescription !== null ? formFields.ShortDescription.toString() : undefined,
            Domain: formFields.Domain !== null ? formFields.Domain.toString() : undefined,
            SubDomain: formFields.SubDomain !== null ? formFields.SubDomain.toString() : undefined,
            CampaignIds: formFields.CampaignIds !== null ? JSON.parse(formFields.CampaignIds.toString() || '[]') as number[] : undefined,
            // User_Id_Settings: user.Id
        };

        // Check for duplicate title (only if Title is being updated)
        if (textFields.Title !== undefined && textFields.Title !== existingArticle.Title) {
            const duplicateTitle = await prisma.articleDetails.findFirst({
                where: {
                    Title: textFields.Title,
                    Id: { not: textFields.Id }
                }
            });

            if (duplicateTitle) {
                return NextResponse.json(
                    { error: "Article title already exists" },
                    { status: 409 }
                );
            }
        }

        // Handle file upload (only if file is provided)
        const file = formFields.file as File | null;
        let imagePath: string | undefined = undefined;

        if (file) {
            try {
                const buffer = Buffer.from(await file.arrayBuffer());
                const mimeType = file.type;
                const originalExtension = mimeType.split('/')[1] as keyof sharp.FormatEnum || 'jpg';
                const uniqueId = uuidv4();

                if (!fs.existsSync(UPLOAD_DIR)) {
                    fs.mkdirSync(UPLOAD_DIR, { recursive: true });
                }

                // Process image versions in parallel
                const [originalBuffer, smallBuffer, mediumBuffer] = await Promise.all([
                    sharp(buffer).toBuffer(),
                    sharp(buffer)
                        .resize(300, null, { fit: 'inside', withoutEnlargement: true })
                        .toFormat(originalExtension, { quality: 80 })
                        .toBuffer(),
                    sharp(buffer)
                        .resize(720, null, { fit: 'inside', withoutEnlargement: true })
                        .toFormat(originalExtension, { quality: 80 })
                        .toBuffer()
                ]);

                // Save all versions
                const baseFileName = `${uniqueId}.${originalExtension}`;
                const versions = [
                    { suffix: '', buffer: originalBuffer },
                    { suffix: '_small', buffer: smallBuffer },
                    { suffix: '_medium', buffer: mediumBuffer }
                ];

                await Promise.all(versions.map(({ suffix, buffer }) => {
                    const fileName = `${uniqueId}${suffix}.${originalExtension}`;
                    const filePath = path.resolve(UPLOAD_DIR, fileName);
                    return fs.promises.writeFile(filePath, buffer);
                }));

                // Delete old image files if they exist
                if (existingArticle.Image) {
                    try {
                        const oldFileName = path.basename(existingArticle.Image);
                        const oldFilePaths = [
                            path.resolve(UPLOAD_DIR, oldFileName),
                            path.resolve(UPLOAD_DIR, oldFileName.replace('.', '_small.')),
                            path.resolve(UPLOAD_DIR, oldFileName.replace('.', '_medium.'))
                        ];

                        await Promise.all(oldFilePaths.map(filePath =>
                            fs.promises.unlink(filePath).catch(() => { })
                        ));
                    } catch (error) {
                        console.error("Error deleting old image files:", error);
                    }
                }

                imagePath = `${baseFileName}`;
            } catch (error: unknown) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown image processing error';
                console.error("Image processing error:", errorMessage);
                return NextResponse.json(
                    { error: "Failed to process image", details: errorMessage },
                    { status: 500 }
                );
            }
        }

        // Generate URL-friendly name only if Title is being updated
        const ShowUrlName = textFields.Title !== undefined
            ? textFields.Title.replace(/[^a-zA-Z0-9]+/g, "-").toLowerCase()
            : undefined;

        // Prepare update data - only include fields that are being updated
        const updateData = {
            UpdatedAt: new Date(),
            ...(textFields.Title !== undefined && { Title: textFields.Title }),
            ...(textFields.Category !== undefined && { Category: textFields.Category }),
            ...(textFields.Url !== undefined && { Url: textFields.Url || null }),
            ...(textFields.Description !== undefined && { Description: textFields.Description || null }),
            ...(textFields.Published !== undefined && { Published: textFields.Published }),
            ...(textFields.ShowArticle !== undefined && { ShowArticle: textFields.ShowArticle }),
            ...(textFields.ShowsAds !== undefined && { ShowsAds: textFields.ShowsAds }),
            ...(textFields.MetaTitle !== undefined && {
                MetaTitle: textFields.MetaTitle || textFields.Title || existingArticle.Title
            }),
            ...(textFields.MetaDescription !== undefined && {
                MetaDescription: textFields.MetaDescription || textFields.Description || existingArticle.Description
            }),
            ...(textFields.MetaKeys !== undefined && { MetaKeys: textFields.MetaKeys }),
            ...(textFields.CustomChannal !== undefined && { CustomChannal: textFields.CustomChannal }),
            ...(textFields.StyleId !== undefined && { StyleId: textFields.StyleId }),
            ...(textFields.AdRelatedSearches !== undefined && { AdRelatedSearches: textFields.AdRelatedSearches }),
            ...(textFields.Remark !== undefined && { Remark: textFields.Remark }),
            ...(imagePath !== undefined && { Image: imagePath }),
            ...(ShowUrlName !== undefined && { ShowUrlName }),
            ...(textFields.ShortDescription !== undefined && { ShortDescription: textFields.ShortDescription }),
            ...(textFields.Domain !== undefined && { Domain: textFields.Domain }),
            ...(textFields.SubDomain !== undefined && { SubDomain: textFields.SubDomain }),
            // User_Id_Settings: user.Id
        };

        // Update article
        const updatedArticle = await prisma.articleDetails.update({
            where: { Id: textFields.Id },
            data: updateData
        });

        // Handle campaign mappings only if CampaignIds is provided
        if (textFields.CampaignIds !== undefined) {
            await prisma.articleCampaignMappings.deleteMany({
                where: { ArticleId: textFields.Id }
            });

            const normalizedCampaignIds = Array.isArray(textFields.CampaignIds)
                ? textFields.CampaignIds.filter(id => id != null && !isNaN(Number(id)))
                : [];

            if (normalizedCampaignIds.length > 0) {
                await prisma.articleCampaignMappings.createMany({
                    data: normalizedCampaignIds.map(campaignId => ({
                        ArticleId: textFields.Id,
                        CampaignId: campaignId,
                        CreatedAt: new Date(),
                    })),
                    skipDuplicates: true
                });
            }
        }

        // Fetch the complete updated article with relations
        const articleWithRelations = await prisma.articleDetails.findUnique({
            where: { Id: textFields.Id },
            include: {
                ArticleCampaignMappings: {
                    select: {
                        CampaignId: true
                    }
                }
            }
        });

        // Get campaign mappings count
        const campaignMappingsCount = await prisma.articleCampaignMappings.count({
            where: { ArticleId: textFields.Id }
        });

        return NextResponse.json({
            success: true,
            message: "Article updated successfully",
            article: articleWithRelations,
            campaignMappings: campaignMappingsCount
        }, { status: 200 });

    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error("Error in article update:", errorMessage);
        return NextResponse.json(
            { error: "Failed to update article", details: errorMessage },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}