const { PrismaClient } = require('./generated/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function main() {
  // Hash password for the admin user
  const hashedPassword = await bcrypt.hash('123456', 10);

  // Create a sample admin user
  const adminUser = await prisma.AdminUser.create({
    data: {
      Name: 'Admin User',
      Email: '<EMAIL>',
      Password: hashedPassword,
      User_Type: 'admin', // Example: 'admin' or 'superadmin'
      Block: false,
      Number: '1234567890',
      Status: true,
      Channels: ['channel1', 'channel2'], // Example channels
      Reports: ['report1', 'report2'], // Example reports
      Domains: ['domain1', 'domain2'], // Example domains
      DisplayName: 'Admin User',
      ProfilePic: 'profilepic_url', // You can add a URL if you want
    },
  });

  console.log('Admin User created:', adminUser);
}

main()
  .catch((e) => {
    console.error('Error:', e);
    throw e;
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
