import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';


export async function DELETE(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { Id } = await req.json();

        if (!Id) {
            return NextResponse.json(
                { error: "Id is required" },
                { status: 400 }
            );
        }

        // Check if the user exists
        const existing = await prisma.category.findUnique({
            where: {
                Id: Id
            }
        });

        if (!existing) {
            return NextResponse.json(
                { error: "Category not found" },
                { status: 404 }
            );
        }

        const updatedUser = await prisma.category.delete({
            where: {
                Id: Id
            },
        });

        return NextResponse.json(
            {
                success: true,
                message: "Category deleted successfully"
            },
            { status: 200 }
        );
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}