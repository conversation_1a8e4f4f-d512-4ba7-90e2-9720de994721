"use client";
import React, { useCallback, useEffect, useState } from "react";
import Swal from "sweetalert2";
import axios from "axios";
import CustomDataTable from "@/components/DataTable/CustomDataTable";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
} from "@mui/material";
import InputGroup from "@/components/FormElements/InputGroup";
import MultiSelectDropdown from "@/components/FormElements/Dropdowns/MultiSelectDropdown";
import { Button } from "@/components/ui-elements/button";
import { FaPlus } from "react-icons/fa";
import { CloseIcon } from "@/assets/icons";

const StyleID = () => {
  const [showLoader, setShowLoader] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showUserModal, setShowUserModal] = useState(false);
  const [editId, setEditId] = useState(null);
  const [formData, setFormData] = useState({
    StyleId: "",
    Name: "",
    Prefix: "",
    AssignUsers: [],
  });
  const [styleIds, setStyleIds] = useState([]);
  const [styleIdsTotalCount, setStyleIdsTotalCount] = useState(0);
  const [styleIdsSearchTerm, setStyleIdsSearchTerm] = useState("");
  const [styleIdsPage, setStyleIdsPage] = useState(0);
  const [styleIdsRowsPerPage, setStyleIdsRowsPerPage] = useState(10);
  const [styleIdsOrder, setStyleIdsOrder] = useState("desc");
  const [styleIdsOrderBy, setStyleIdsOrderBy] = useState("");
  const [selectedStyleId, setSelectedStyleId] = useState("");
  const [mappings, setMappings] = useState({
    isLoading: false,
    mappingsData: [],
  });
  const [mappingsTotalCount, setMappingsTotalCount] = useState(0);
  const [mappingsSearchTerm, setMappingsSearchTerm] = useState("");
  const [mappingsPage, setMappingsPage] = useState(0);
  const [mappingsRowsPerPage, setMappingsRowsPerPage] = useState(10);
  const [mappingsOrder, setMappingsOrder] = useState("desc");
  const [mappingsOrderBy, setMappingsOrderBy] = useState("");
  
  const [users, setUsers] = useState([]);

  const columns = [
    {
      id: "Name",
      label: "Name",
    },
    {
      id: "Prefix",
      label: "Prefix",
    },
    {
      id: "UserCount",
      label: "Users",
    },
  ];

  const mappingColumns = [
    {
      id: "UserName",
      label: "User",
    },
    {
      id: "UserEmail",
      label: "Email",
    },
    {
      id: "UserType",
      label: "User Type",
    },
    {
      id: "CreatedAt",
      label: "Assigned On",
    },
  ];

  const fetchStyleIds = useCallback(async () => {
    try {
      setShowLoader(true);
      const response = await axios.get("/api/StyleIds/get", {
        params: {
          page: styleIdsPage + 1,
          length: styleIdsRowsPerPage,
          q: styleIdsSearchTerm,
          orderBy: styleIdsOrderBy,
          orderDir: styleIdsOrder,
        },
        withCredentials: true,
      });

      if (response.data.success) {
        setStyleIds(response.data.data);
        setStyleIdsTotalCount(
          response.data.pagination?.recordsFiltered ||
            response.data.data.length,
        );
      } else {
        setStyleIds([]);
        setStyleIdsTotalCount(0);
      }
    } catch (error) {
      console.error("Error fetching style IDs:", error);
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Error fetching style IDs",
        timer: 3000,
        showConfirmButton: false,
      });
      setStyleIds([]);
      setStyleIdsTotalCount(0);
    } finally {
      setShowLoader(false);
    }
  }, [styleIdsPage, styleIdsRowsPerPage, styleIdsSearchTerm, styleIdsOrder, styleIdsOrderBy]);

  const fetchMappingsData = useCallback(async () => {
    if (!selectedStyleId) {
      setMappings((prev) => ({ ...prev, isLoading: false, mappingsData: [] }));
      return;
    }

    try {
      setMappings((prev) => ({ ...prev, isLoading: true }));
      const response = await axios.get("/api/StyleIdUserMapping/get", {
        params: {
          Id: selectedStyleId,
          q: mappingsSearchTerm,
          page: mappingsPage + 1,
          limit: mappingsRowsPerPage,
          orderBy: mappingsOrderBy,
          orderDir: mappingsOrder,
        },
        withCredentials: true,
      });

      const transformedData = response.data.data.map((item) => ({
        ...item,
        CreatedAt: new Date(item.CreatedAt).toLocaleString(),
      }));

      setMappings((prev) => ({
        ...prev,
        isLoading: false,
        mappingsData: transformedData,
      }));
      setMappingsTotalCount(
        response.data.pagination?.recordsFiltered ||
          response.data.data?.length ||
          0,
      );
    } catch (error) {
      console.error("Error fetching user mappings:", error);
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to load user mappings",
        timer: 3000,
        showConfirmButton: false,
      });
      setMappings((prev) => ({ ...prev, isLoading: false, mappingsData: [] }));
    }
  }, [selectedStyleId, mappingsPage, mappingsRowsPerPage, mappingsSearchTerm, mappingsOrder, mappingsOrderBy]);

  const fetchUsers = useCallback(async () => {
    try {
      const response = await axios.get("/api/adminuser/GetDropdown", {
        withCredentials: true,
      });

      if (response?.status === 200) {
        const userData = response.data.data.map((user) => ({
          value: user.Id,
          label: `${user.Name} (${user.Email})`,
        }));
        setUsers(userData);
      }
    } catch (error) {
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: "Failed to load users. Please try again.",
        timer: 3000,
        showConfirmButton: false,
      });
    }
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === "Prefix" ? value.slice(0, 5) : value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.StyleId || !formData.Name || !formData.Prefix) {
      await Swal.fire({
        icon: "error",
        title: "Validation Error",
        text: "All fields are required.",
        timer: 3000,
        showConfirmButton: false,
      });
      return;
    }

    if (isNaN(formData.StyleId)) {
      await Swal.fire({
        icon: "error",
        title: "Validation Error",
        text: "Style ID must be a number.",
        timer: 3000,
        showConfirmButton: false,
      });
      return;
    }

    try {
      setShowLoader(true);
      const payload = {
        StyleId: formData.StyleId,
        Name: formData.Name,
        Prefix: formData.Prefix,
        AssignUsers: formData.AssignUsers,
      };

      if (editId) {
        await axios.put(
          `/api/StyleIds/edit`,
          { Id: editId, ...payload },
          { withCredentials: true },
        );
        await Swal.fire({
          icon: "success",
          title: "Success",
          text: "Style ID updated successfully.",
          timer: 2000,
          showConfirmButton: false,
        });
      } else {
        await axios.post("/api/StyleIds/add", payload, {
          withCredentials: true,
        });
        await Swal.fire({
          icon: "success",
          title: "Success",
          text: "Style ID added successfully.",
          timer: 2000,
          showConfirmButton: false,
        });
      }

      resetForm();
      setShowModal(false);
      fetchStyleIds();
      if (selectedStyleId) {
        fetchMappingsData();
      }
    } catch (error) {
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Error processing style ID.",
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setShowLoader(false);
    }
  };

  const resetForm = useCallback(() => {
    setFormData({
      StyleId: "",
      Name: "",
      Prefix: "",
      AssignUsers: [],
    });
    setEditId(null);
  }, []);

  const handleViewUsers = useCallback(async (rowData) => {
    setSelectedStyleId(rowData.Id);
    setShowUserModal(true);
    // Reset mappings table state when opening
    setMappingsPage(0);
    setMappingsRowsPerPage(10);
    setMappingsSearchTerm("");
    setMappingsOrder("desc");
    setMappingsOrderBy("");
  }, []);

  const handleEdit = useCallback(async (rowData) => {
    try {
      const loadingAlert = Swal.fire({
        title: "Loading Style Data",
        html: "Please wait while we fetch the details...",
        allowOutsideClick: false,
        didOpen: () => Swal.showLoading(),
        backdrop: true,
      });

      const response = await axios.get(
        `/api/StyleIds/GetById?id=${rowData.Id}`,
      );
      const styleData = response.data.data[0];

      setFormData({
        StyleId: styleData.StyleId || "",
        Name: styleData.Name || "",
        Prefix: styleData.Prefix || "",
        AssignUsers: styleData.UserMappings || [],
      });

      setEditId(styleData.Id);
      setShowModal(true);

      await loadingAlert.close();
    } catch (error) {
      console.error("Error fetching style data:", error);

      Swal.close();

      Swal.fire({
        title: "Error",
        text: error.response?.data?.message || "Failed to load style data",
        icon: "error",
        confirmButtonText: "OK",
        confirmButtonColor: "#5750f1",
      });
    } 
  }, []);

  const handleDelete = useCallback(
    async (rowData) => {
      const result = await Swal.fire({
        icon: "warning",
        title: "Confirm Deletion",
        text: "Are you sure you want to delete this Style ID?",
        showCancelButton: true,
        showCloseButton: true,
        confirmButtonColor: "#5750f1",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, delete it!",
      });

      if (result.isConfirmed) {
        try {
          setShowLoader(true);
          await axios.delete("/api/StyleIds/Delete", {
            data: { Id: rowData.Id },
            withCredentials: true,
          });
          await Swal.fire({
            icon: "success",
            title: "Success",
            text: "Style ID deleted successfully.",
            timer: 2000,
            showConfirmButton: false,
          });
          fetchStyleIds();
          if (rowData.Id === selectedStyleId) {
            setSelectedStyleId("");
            setMappings({ mappingsData: [] });
          }
        } catch (error) {
          await Swal.fire({
            icon: "error",
            title: "Error",
            text: error?.response?.data?.error || "Error deleting style ID.",
            timer: 3000,
            showConfirmButton: false,
          });
        } finally {
          setShowLoader(false);
        }
      }
    },
    [selectedStyleId, fetchStyleIds],
  );

  const handleRemoveMapping = async (rowData) => {
    const result = await Swal.fire({
      icon: "warning",
      title: "Confirm Removal",
      text: "Are you sure you want to remove this user assignment?",
      showCancelButton: true,
      confirmButtonColor: "#5750f1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, remove it!",
    });

    if (result.isConfirmed) {
      try {
        await axios.delete("/api/StyleIdUserMapping/delete", {
          data: { Id: rowData.Id },
          withCredentials: true,
        });
        await Swal.fire({
          icon: "success",
          title: "Success",
          text: "User assignment removed successfully",
          timer: 2000,
          showConfirmButton: false,
        });

        setShowUserModal(false);
        setSelectedStyleId("");
        setMappings({ mappingsData: [] });
        fetchStyleIds();
      } catch (error) {
        await Swal.fire({
          icon: "error",
          title: "Error",
          text:
            error?.response?.data?.error || "Failed to remove user assignment",
          timer: 3000,
          showConfirmButton: false,
        });
      }
    }
  };

  useEffect(() => {
    fetchStyleIds();
  }, [fetchStyleIds]);

  useEffect(() => {
    if (showUserModal) {
      fetchMappingsData();
    }
  }, [showUserModal, fetchMappingsData]);

  useEffect(() => {
    if (showModal) {
      fetchUsers();
    }
  }, [showModal, fetchUsers]);

  return (
    <>
      <div className="rounded-[10px] border border-stroke bg-white p-4 shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card sm:p-6">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <h2 className="text-2xl font-bold text-dark dark:text-white">
            Style IDs
          </h2>
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
            <Button
              type="button"
              label="Add Style ID"
              variant="primary"
              shape="rounded"
              icon={<FaPlus size={14} />}
              onClick={() => {
                resetForm();
                setShowModal(true);
              }}
            />
          </div>
        </div>
        <CustomDataTable
          isLoading={showLoader}
          columns={columns}
          rows={styleIds}
          searchTerm={styleIdsSearchTerm}
          onSearchChange={setStyleIdsSearchTerm}
          page={styleIdsPage}
          rowsPerPage={styleIdsRowsPerPage}
          onPageChange={setStyleIdsPage}
          onRowsPerPageChange={setStyleIdsRowsPerPage}
          totalCount={styleIdsTotalCount}
          order={styleIdsOrder}
          orderBy={styleIdsOrderBy}
          onRequestSort={(event, property) => {
            const isAsc = styleIdsOrderBy === property && styleIdsOrder === "asc";
            setStyleIdsOrder(isAsc ? "desc" : "asc");
            setStyleIdsOrderBy(property);
          }}
          onEdit={handleEdit}
          onDelete={handleDelete}
          handleUserCountClick={handleViewUsers}
        />
      </div>

      <Dialog
        open={showModal}
        onClose={() => {
          setShowModal(false);
          resetForm();
        }}
        fullWidth
        maxWidth="sm"
        PaperProps={{
          sx: {
            maxHeight: "90vh",
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            color: "white",
            py: 2,
            px: 3,
          }}
          className="bg-primary text-white"
        >
          <span style={{ fontSize: "1.25rem", fontWeight: 500 }}>
            {editId ? "Edit Style ID" : "Add Style ID"}
          </span>
          <IconButton
            aria-label="close"
            onClick={() => {
              setShowModal(false);
              resetForm();
            }}
            sx={{
              color: "white",
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent dividers sx={{ py: 3, px: 3 }}>
          <form>
              <InputGroup
                label="Style ID (Numeric)"
                type="number"
                name="StyleId"
                value={formData.StyleId}
                handleChange={handleChange}
                placeholder="Enter Style ID"
                required
              />

              <InputGroup
                label="Name"
                type="text"
                name="Name"
                value={formData.Name}
                handleChange={handleChange}
                placeholder="Enter Name"
                required
              />
              <MultiSelectDropdown
                label="Assign Users"
                options={users}
                value={formData.AssignUsers}
                onChange={(selectedIds) =>
                  setFormData({ ...formData, AssignUsers: selectedIds })
                }
                displayKey="label"
                idKey="value"
                placeholder="Select users..."
                showSelectAll={true}
              />

              <InputGroup
                label="Prefix (max 5 characters)"
                type="text"
                name="Prefix"
                value={formData.Prefix}
                handleChange={handleChange}
                placeholder="Enter Prefix (e.g., st)"
                required
              />
          </form>
        </DialogContent>

        <DialogActions sx={{ py: 2, px: 3 }}>
          <Button
            type="submit"
            label={
              showLoader
                ? "Loading..."
                : editId
                  ? "Update Style ID"
                  : "Add Style ID"
            }
            variant="primary"
            shape="rounded"
            onClick={handleSubmit}
          />
        </DialogActions>
      </Dialog>

      <Dialog
        open={showUserModal}
        onClose={() => {
          setShowUserModal(false);
          setSelectedStyleId("");
          setMappings({ mappingsData: [] });
          setMappingsSearchTerm("");
        }}
        fullWidth
        maxWidth="md"
        PaperProps={{
          sx: {
            maxHeight: "90vh",
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            color: "white",
            py: 2,
            px: 3,
          }}
          className="bg-primary text-white"
        >
          <span style={{ fontSize: "1.25rem", fontWeight: 500 }}>
            User Mappings for Style ID
          </span>
          <IconButton
            aria-label="close"
            onClick={() => {
              setShowUserModal(false);
              setSelectedStyleId("");
              setMappings({ mappingsData: [] });
              setMappingsSearchTerm("");
            }}
            sx={{
              color: "white",
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers sx={{ py: 3, px: 3 }}>
          <CustomDataTable
            isLoading={mappings.isLoading}
            columns={mappingColumns}
            rows={mappings.mappingsData}
            searchTerm={mappingsSearchTerm}
            onSearchChange={setMappingsSearchTerm}
            page={mappingsPage}
            rowsPerPage={mappingsRowsPerPage}
            onPageChange={setMappingsPage}
            onRowsPerPageChange={setMappingsRowsPerPage}
            totalCount={mappingsTotalCount}
            order={mappingsOrder}
            orderBy={mappingsOrderBy}
            onRequestSort={(event, property) => {
              const isAsc = mappingsOrderBy === property && mappingsOrder === "asc";
              setMappingsOrder(isAsc ? "desc" : "asc");
              setMappingsOrderBy(property);
            }}
            onDelete={handleRemoveMapping}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};

export default StyleID;