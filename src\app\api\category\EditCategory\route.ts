import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';


export async function PUT(req: NextRequest) {
    const user = await verifyToken(req);

    if (!user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    try {
     
        const { Name, Id } = await req.json();

        if (!Name) {
            return NextResponse.json(
                { error: "Missing required fields" },
                { status: 400 }
            );
        }

        // Check if category exists
        const existingCategory = await prisma.category.findUnique({
            where: { Id }
        });

        if (!existingCategory) {
            return NextResponse.json(
                { error: "Category not found" },
                { status: 404 }
            );
        }
        const showUrlName = Name ? Name.replace(/[^a-zA-Z0-9]+/g, '-')?.toLowerCase() : '';
        // Update the category
        const updatedCategory = await prisma.category.update({
            where: { Id },
            data: { Name: Name, ShowUrlName: showUrlName },
            select: {
                Id: true,
                Name: true,
            },
        });

        return NextResponse.json(
            {
                success: true,
                data: updatedCategory
            }, { status: 200 });
    } catch (error) {
        console.error("Error updating category:", error instanceof Error ? error.message : String(error));
        return NextResponse.json(
            { error: "Failed to update category" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}