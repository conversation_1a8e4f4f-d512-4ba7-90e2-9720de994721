import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import bcrypt from 'bcrypt';


export async function POST(req: NextRequest) {
    try {

        type AuthenticatedUser = {
            Id: string;
        };

        const user = await verifyToken(req) as AuthenticatedUser;
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { StyleId, Name, Prefix, AssignUsers } = await req.json();

        if (!StyleId || !Name || !Prefix) {
            return NextResponse.json(
                { error: "StyleId, Name, and Prefix are required" },
                { status: 400 }
            );
        }

        // Validate AssignUsers before proceeding
        if (!AssignUsers || !Array.isArray(AssignUsers) || AssignUsers.length === 0) {
            return NextResponse.json(
                { error: "AssignUsers must be a non-empty array" },
                { status: 400 }
            );
        }

        // Check if StyleId already exists
        const existing = await prisma.styleIds.findFirst({
            where: { StyleId }
        });

        if (existing) {
            return NextResponse.json(
                { error: "StyleId already exists" },
                { status: 409 }
            );
        }

        // Validate user IDs before starting transaction
        const userIds = AssignUsers.map(userId => userId);
        const existingUsers = await prisma.adminUser.findMany({
            where: {
                Id: {
                    in: userIds
                },
                IsDeleted: false
            },
            select: {
                Id: true
            }
        });

        if (existingUsers.length !== userIds.length) {
            return NextResponse.json(
                { error: "One or more user IDs are invalid" },
                { status: 400 }
            );
        }

        // Use a transaction to ensure both operations succeed or fail together
        const result = await prisma.$transaction(async (prismaTransaction) => {
            // 1. Create the styleIds record
            const newStyleId = await prismaTransaction.styleIds.create({
                data: {
                    StyleId,
                    Name,
                    Prefix,
                    CreatedBy: user.Id
                }
            });

            // await prismaTransaction.StyleIdUserMappings.deleteMany({
            //     where: {
            //         StyleId:someConversionFunction(StyleId) 
            //     }
            // });

            // 2. Create mappings for each user individually
            const createdMappings = [];
            for (const userId of AssignUsers) {

                const mapping = await prismaTransaction.styleIdUserMappings.create({
                    data: {
                        StyleId: newStyleId.Id,
                        UserId: userId
                    }
                });
                createdMappings.push(mapping);
            }

            return {
                styleId: newStyleId,
                mappings: createdMappings
            };
        });

        return NextResponse.json(
            {
                success: true,
                message: "StyleId and user mappings created successfully",
                mappingsCreated: result.mappings.length
            },
            { status: 201 }
        );
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}