import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const search = searchParams.get("q");
        const orderBy = searchParams.get("orderBy") || "CreatedAt";
        const orderDir = searchParams.get("orderDir") || "asc";
        const userId = searchParams.get("Id");

        let skip: number | undefined;
        let limit: number | undefined;

        if (length == -1) {
            skip = undefined;
            limit = undefined;
        }
        else {
            const skipCount = (start == 1 ? 0 : start - 1) * length;
            limit = length;
            skip = (skipCount > 0 ? skipCount : 0);
        }

        if (!userId) {
            return NextResponse.json(
                { error: "styleId parameter is required" },
                { status: 400 }
            );
        }

        // Base filter
        let where: any = {
            UserId: userId,
            StyleIds: {
                IsDeleted: false
            }
        };

        type OrderByType = {

        };

        const orderField = orderBy.toLowerCase();

        let orderByClause: OrderByType = {};

        if (orderField === "name") {
            orderByClause = { StyleIds: { Name: orderDir.toLowerCase() as 'asc' | 'desc' } };
        } else if (orderField === "styleid") {
            orderByClause = { StyleIds: { StyleId: orderDir.toLowerCase() as 'asc' | 'desc' } };
        } else if (orderField === "prefix") {
            orderByClause = { StyleIds: { Prefix: orderDir.toLowerCase() as 'asc' | 'desc' } };
        } else if (orderField === "createdat") {
            orderByClause = { CreatedAt: orderDir.toLowerCase() as 'asc' | 'desc' };
        }
        else {
            orderByClause = { [orderField]: orderDir.toLowerCase() as 'asc' | 'desc' };
        }

        if (search) {
            where = {
                ...where,
                StyleIds: {
                    OR: [
                        { StyleId: { contains: search, mode: 'insensitive' } },
                        { Name: { contains: search, mode: 'insensitive' } },
                        { Prefix: { contains: search, mode: 'insensitive' } }
                    ]
                }
            };
        }

        const recordsTotal = await prisma.styleIdUserMappings.count({ where });

        const recordsFiltered = await prisma.styleIdUserMappings.count({ where });


        // Get paginated data
        const mappings = await prisma.styleIdUserMappings.findMany({
            where,
            skip,
            take: limit,
            orderBy: orderByClause,
            include: {
                StyleIds: {
                    select: {
                        StyleId: true,
                        Name: true,
                        Prefix: true,
                    }
                }
            }
        });

        // Transform data
        const transformedData = mappings.map(mapping => ({
            Id: mapping.Id,
            CreatedAt: mapping.CreatedAt,
            ...(mapping.StyleIds ? {
                StyleId: mapping.StyleIds.StyleId,
                Name: mapping.StyleIds.Name,
                Prefix: mapping.StyleIds.Prefix
            } : {})
        }));

        return NextResponse.json({
            success: true,
            data: transformedData,
            pagination: {
                draw: parseInt(searchParams.get("draw") || "1"),
                recordsFiltered,
                //recordsTotal,
                currentPageCount: transformedData.length,
                start,
                length,
                currentPage: start,
                totalPages: length === -1 ? 1 : Math.ceil(recordsFiltered / (length || 1)),
                hasNextPage: length === -1 ? false : start * length < recordsFiltered,
                hasPreviousPage: start > 1,
            }
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            {
                error: "Internal Server Error",
                details: error instanceof Error ? error.message : String(error)
            },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}