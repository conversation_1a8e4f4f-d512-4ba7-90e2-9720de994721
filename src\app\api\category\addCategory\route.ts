import { prisma } from '../../../../lib/prisma'; // Prisma client import
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';



export async function POST(req: NextRequest) {
   const user = await verifyToken(req);

    if (!user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    try {
        const { name } = await req.json();

        if (!name) {
            return NextResponse.json(
                { error: "Missing required fields" },
                { status: 400 }
            );
        }

        // Create showUrlName from the name, replace non-alphanumeric characters with '-'
        const showUrlName = name.replace(/[^a-zA-Z0-9]+/g, '-')?.toLowerCase();

        // Check if category with the same name already exists
        const existingCategory = await prisma.category.findFirst({
            where: { Name: name },
        });

        if (existingCategory) {
            return NextResponse.json(
                { error: "Category name already exists" },
                { status: 409 }
            );
        }

        // Create the new category in Prisma
        const newCategory = await prisma.category.create({
            data: {
                Name: name,
                ShowUrlName: showUrlName,
            },
        });

        // Respond with the newly created category
        return NextResponse.json({
            success: true,
            data: newCategory,
        });

    } catch (err) {
        console.error('Add Category Error:', err);
        return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
    } finally {
        await prisma.$disconnect();
    }
}
