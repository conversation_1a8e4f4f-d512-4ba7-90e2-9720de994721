import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

interface AdminUserWithCounts {
    Id: string;
    Name: string | null;
    Email: string | null;
    Number: string | null;
    User_Type: string | null;
    Status: boolean | null;
    subDomainCount: number;
    styleIdCount: number;
}

export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const search = searchParams.get("q") || '';
        const orderBy = searchParams.get("orderBy") || "CreatedAt";
        const orderDir = searchParams.get("orderDir") || "asc";

        let skip: number | undefined;
        let limit: number | undefined;

        if (length == -1) {
            skip = undefined;
            limit = undefined;
        }
        else {
            const skipCount = (start == 1 ? 0 : start - 1) * length;
            limit = length;
            skip = (skipCount > 0 ? skipCount : 0);
        }

        let where: any = {
            IsDeleted: false,
        };
        if (search) {
            where.OR = [
                { Name: { contains: search, mode: 'insensitive' } },
                { Email: { contains: search, mode: 'insensitive' } },
                { Number: { contains: search } }
            ];
        }

        // First get the count of filtered records
        const recordsTotal = await prisma.adminUser.count({
            where: { IsDeleted: false }
        });

        const recordsFiltered = await prisma.adminUser.count({ where });

        // Fetch all users that match the filter (without pagination)
        const allUsers = await prisma.adminUser.findMany({
            where,
            select: {
                Id: true,
                Name: true,
                Email: true,
                Number: true,
                User_Type: true,
                Block: true,
                Status: true,
                CreatedAt: true,
            }
        });


        let usersWithCounts: AdminUserWithCounts[] = await Promise.all(
            allUsers.map(async (user) => {
                const subDomainCount = await prisma.subDomainUserMappings.count({
                    where: {
                        UserId: user.Id,
                        SubDomain: {
                            IsDeleted: false,
                            Domain_SubDomain_DomainToDomain: {
                                  IsDeleted: false,
                            }
                        }
                    }
                });

                const styleIdCount = await prisma.styleIdUserMappings.count({
                    where: {
                        UserId: user.Id,
                        StyleIds: {
                            IsDeleted: false
                        }
                    }
                });

                return {
                    ...user,
                    subDomainCount,
                    styleIdCount
                };
            })
        );


        // Apply sorting based on orderBy and orderDir
        const allowedFields = ['Id', 'Name', 'Email', 'Number', 'User_Type', 'Status', 'CreatedAt', 'subDomainCount', 'styleIdCount'];
        if (allowedFields.includes(orderBy)) {
            usersWithCounts.sort((a, b) => {
                const aValue = a[orderBy as keyof AdminUserWithCounts];
                const bValue = b[orderBy as keyof AdminUserWithCounts];

                // Handle null/undefined values
                if (aValue === null || aValue === undefined) return orderDir === 'asc' ? 1 : -1;
                if (bValue === null || bValue === undefined) return orderDir === 'asc' ? -1 : 1;

                // Numeric comparison for counts
                if (orderBy === 'subDomainCount' || orderBy === 'styleIdCount') {
                    return orderDir === 'asc'
                        ? (aValue as number) - (bValue as number)
                        : (bValue as number) - (aValue as number);
                }

                // Boolean comparison for Status
                else if (orderBy === 'Status') {
                    const aBool = aValue as boolean;
                    const bBool = bValue as boolean;
                    return orderDir === 'asc'
                        ? (aBool === bBool ? 0 : aBool ? 1 : -1)
                        : (aBool === bBool ? 0 : aBool ? -1 : 1);
                }
                // String comparison for other fields
                else {
                    return orderDir === 'asc'
                        ? String(aValue).localeCompare(String(bValue))
                        : String(bValue).localeCompare(String(aValue));
                }
            });
        }

        // Apply pagination after sorting
        const paginatedUsers = length === -1
            ? usersWithCounts
            : usersWithCounts.slice(skip || 0, (skip || 0) + (limit || usersWithCounts.length));

        return NextResponse.json({
            success: true,
            data: paginatedUsers,
            pagination: {
                draw: parseInt(searchParams.get("draw") || "1"),
                recordsFiltered,
                currentPageCount: paginatedUsers.length,
                start,
                length,
                currentPage: start,
                totalPages: length === -1 ? 1 : Math.ceil(recordsFiltered / (length || 1)),
                hasNextPage: length === -1 ? false : start * length < recordsFiltered,
                hasPreviousPage: start > 1,
            }
        });

    } catch (error) {
        console.error("Error fetching users:", error);
        return NextResponse.json(
            { error: "User Not Found" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}