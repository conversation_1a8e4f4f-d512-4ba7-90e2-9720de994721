import axios from 'axios';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

// Interfaces
interface UserRequest {
    budgetName: string;
    budgetAmountMicros: string;
    campaignName: string;
    status?: string;
    customerId: string;
}

interface BudgetResponse {
    results: {
        resourceName: string;
    }[];
}

interface CampaignResponse {
    results: {
        resourceName: string;
    }[];
}

interface CampaignDetails {
    campaign: {
        id: string;
        name: string;
        status: string;
        advertising_channel_type: string;
    };
    campaign_budget: {
        amount_micros: string;
        name: string;
        status: string;
    };
}

interface BudgetOperation {
    create: {
        name: string;
        amount_micros: number;
        delivery_method: string;
    };
}

interface CampaignOperation {
    create: {
        name: string;
        advertising_channel_type: string;
        status: string;
        campaign_budget: string;
        manual_cpc: {
            enhanced_cpc_enabled: boolean;
        };
    };
}

interface MutateRequest {
    customer_id: string;
    operations: any[];
}

export class GoogleAdsService {
    private readonly authToken: string;
    private readonly devToken: string;
    private readonly loginCustomerId: string;

    constructor() {
        const tokenPath = path.join(process.cwd(), 'public', 'tokens.json');
        if (!fs.existsSync(tokenPath)) {
            throw new Error(`token.json not found at ${tokenPath}`);
        }

        const tokenData = JSON.parse(fs.readFileSync(tokenPath, 'utf-8'));
        if (!tokenData.access_token) {
            throw new Error('access_token not found in token.json');
        }

        if (!process.env.GOOGLE_ADS_DEV_TOKEN || !process.env.GOOGLE_ADS_LOGIN_CUSTOMER_ID) {
            throw new Error('Missing required Google Ads API configuration in environment');
        }

        this.authToken = `Bearer ${tokenData.access_token}`;
        this.devToken = process.env.GOOGLE_ADS_DEV_TOKEN;
        this.loginCustomerId = process.env.GOOGLE_ADS_LOGIN_CUSTOMER_ID;

        console.log( this.authToken, " this.authToken");
        
    }


    /**
     * Main method to create a campaign
     */
    async createCampaign(userRequest: UserRequest) {
        try {
            // Validate user request
            this.validateUserRequest(userRequest);

            // 1. Create Budget
            const budgetResourceName = await this.createBudget(userRequest);

            // 2. Create Campaign
            const { data, campaignId } = await this.createCampaignWithBudget(userRequest, budgetResourceName);

            // 3. Fetch campaign details
            const campaignDetails = await this.fetchCampaignDetails(userRequest.customerId, campaignId);

            return {
                success: true,
                campaignId: campaignId,
                data: data,
                campaignDetails: campaignDetails
            };
        } catch (error) {
            console.error('Error in createCampaign:', error);
            throw error;
        }
    }

    /**
     * Creates a campaign budget
     */
    private async createBudget({ budgetName, budgetAmountMicros, customerId }: UserRequest): Promise<string> {
        try {
            const budgetPayload: MutateRequest = {
                customer_id: customerId,
                operations: [{
                    create: {
                        name: budgetName,
                        amount_micros: Number(budgetAmountMicros),
                        delivery_method: 'STANDARD',
                    }
                }]
            };

            const budgetResponse = await axios.post<BudgetResponse>(
                `https://googleads.googleapis.com/v19/customers/${customerId}/campaignBudgets:mutate`,
                budgetPayload,
                {
                    headers: this.getHeaders(),
                }
            );

            if (!budgetResponse.data?.results?.[0]?.resourceName) {
                throw new Error('Failed to create budget: No resource name returned');
            }

            return budgetResponse.data.results[0].resourceName;
        } catch (error) {
            console.error('Error in createBudget:', error);
            throw error;
        }
    }

    /**
     * Creates a campaign with the specified budget
     */
    private async createCampaignWithBudget({ campaignName, customerId, status }: UserRequest, budgetResourceName: string) {
        try {
            const campaignPayload: MutateRequest = {
                customer_id: customerId,
                operations: [{
                    create: {
                        name: campaignName,
                        advertising_channel_type: "SEARCH",
                        status: status || "PAUSED",
                        campaign_budget: budgetResourceName,
                        manual_cpc: {
                            enhanced_cpc_enabled: false,
                        },
                    }
                }]
            };

            const response = await axios.post<CampaignResponse>(
                `https://googleads.googleapis.com/v19/customers/${customerId}/campaigns:mutate`,
                campaignPayload,
                {
                    headers: this.getHeaders(),
                }
            );

            if (!response.data?.results?.[0]?.resourceName) {
                throw new Error('Failed to create campaign: No resource name returned');
            }

            const campaignId = response.data.results[0].resourceName.split('/')[3];
            return {
                data: response.data,
                campaignId: campaignId
            };
        } catch (error) {
            console.error('Error in createCampaignWithBudget:', error);
            throw error;
        }
    }

    /**
     * Fetches campaign details
     */
    async fetchCampaignDetails(customerId: string, campaignId: string): Promise<CampaignDetails> {
        try {
            const searchRequest = {
                query: `
                    SELECT 
                        campaign.id, 
                        campaign.name, 
                        campaign.status,
                        campaign.advertising_channel_type,
                        campaign_budget.amount_micros,
                        campaign_budget.name,
                        campaign_budget.status
                    FROM campaign 
                    WHERE campaign.id = ${campaignId}`
            };

            const response = await axios.post(
                `https://googleads.googleapis.com/v19/customers/${customerId}/googleAds:search`,
                searchRequest,
                {
                    headers: this.getHeaders(),
                }
            );

            if (!response.data?.results?.[0]) {
                throw new Error(`No campaign found with ID: ${campaignId}`);
            }

            return response.data.results[0];
        } catch (error) {
            console.error('Error in fetchCampaignDetails:', error);
            throw error;
        }
    }

    /**
     * Returns the headers needed for Google Ads API requests
     */
    private getHeaders() {
        return {
            'Authorization': this.authToken,
            'developer-token': this.devToken,
            'Content-Type': 'application/json',
            'login-customer-id': this.loginCustomerId
        };
    }

    /**
     * Validates user request parameters
     */
    private validateUserRequest(request: UserRequest) {
        if (!request.customerId) {
            throw new Error('Customer ID is required');
        }
        if (!request.budgetName) {
            throw new Error('Budget name is required');
        }
        if (!request.budgetAmountMicros || isNaN(Number(request.budgetAmountMicros))) {
            throw new Error('Valid budget amount is required');
        }
        if (!request.campaignName) {
            throw new Error('Campaign name is required');
        }
    }
}