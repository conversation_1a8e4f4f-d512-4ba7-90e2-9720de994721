import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Interfaces
interface UserRequest {
    budgetName: string;
    budgetAmountMicros: string;
    campaignName: string;
    status: string;
    customerId: string;
}

interface BudgetResponse {
    results: {
        resourceName: string;
    }[];
}

interface CampaignResponse {
    results: {
        resourceName: string;
    }[];
}

interface CampaignDetails {
    campaign: {
        id: string;
        name: string;
        status: string;
        advertising_channel_type: string;
        primary_status?: string;
        primary_status_reasons?: string[];
        optimization_score?: number;
        bidding_strategy_type?: string;
        start_date?: string;
        end_date?: string;
        experiment_type?: string;
        serving_status?: string;
        network_settings?: {
            target_google_search: boolean;
            target_search_network: boolean;
            target_content_network: boolean;
            target_partner_search_network: boolean;
            target_youtube: boolean;
            target_google_tv_network: boolean;
        };
        geo_target_type_setting?: {
            positive_geo_target_type: string;
            negative_geo_target_type: string;
        };
        maximize_conversions?: {
            target_cpa_micros: string;
        };
        payment_mode?: string;
        url_expansion_opt_out?: boolean;
        audience_setting?: {
            use_audience_grouped: boolean;
        };
        brand_guidelines_enabled?: boolean;
    };
    campaign_budget: {
        amount_micros: string;
        name: string;
        status: string;
        delivery_method?: string;
        explicitly_shared?: boolean;
        has_recommended_budget?: boolean;
        id: string;
        period?: string;
        recommended_budget_amount_micros?: string;
        reference_count?: number;
        type?: string;
        total_amount_micros?: string;
    };
    metrics?: {
        [key: string]: any;
    };
    customer?: {
        descriptive_name: string;
        id: string;
        currency_code: string;
    };
}

export class GoogleAdsService {
    private readonly authToken: string;
    private readonly devToken: string;
    private readonly loginCustomerId: string;

    constructor() {
        // Validate required environment variables
        if (!process.env.GOOGLE_ADS_AUTH_TOKEN || 
            !process.env.GOOGLE_ADS_DEV_TOKEN || 
            !process.env.GOOGLE_ADS_LOGIN_CUSTOMER_ID) {
            throw new Error('Missing required Google Ads API configuration');
        }

        this.authToken = `Bearer ${process.env.GOOGLE_ADS_AUTH_TOKEN}`;
        this.devToken = process.env.GOOGLE_ADS_DEV_TOKEN;
        this.loginCustomerId = process.env.GOOGLE_ADS_LOGIN_CUSTOMER_ID;
    }

    /**
     * Main method to create a campaign
     */
    async createCampaign(userRequest: UserRequest) {
        try {
            // Validate user request
            this.validateUserRequest(userRequest);

            // 1. Create Budget
            const budgetResourceName = await this.createBudget(userRequest);

            // 2. Create Campaign
            const { data, campaignId } = await this.createCampaignWithBudget(userRequest, budgetResourceName);

            // 3. Fetch campaign details
            const campaignDetails = await this.fetchCampaignDetails(userRequest.customerId, campaignId);

            return {
                success: true,
                campaignId: campaignId,
                data: data,
                campaignDetails: campaignDetails
            };
        } catch (error) {
            console.error('Error in createCampaign:', error);
            throw (error);
        }
    }

    /**
     * Creates a campaign budget
     */
    private async createBudget({ budgetName, budgetAmountMicros, customerId }: UserRequest): Promise<string> {
        try {
            const budgetPayload = {
                operations: [
                    {
                        create: {
                            name: budgetName,
                            amountMicros: parseInt(budgetAmountMicros),
                            deliveryMethod: 'STANDARD',
                        },
                    },
                ],
            };

            const budgetResponse = await axios.post<BudgetResponse>(
                `https://googleads.googleapis.com/v19/customers/${customerId}/campaignBudgets:mutate`,
                budgetPayload,
                {
                    headers: this.getHeaders(),
                }
            );

            if (!budgetResponse.data?.results?.[0]?.resourceName) {
                throw new Error('Failed to create budget: No resource name returned');
            }

            return budgetResponse.data.results[0].resourceName;
        } catch (error) {
            console.error('Error in createBudget:', error);
            throw (error);
        }
    }

    /**
     * Creates a campaign with the specified budget
     */
    private async createCampaignWithBudget({ campaignName, customerId, status }: UserRequest, budgetResourceName: string) {
        try {
            const campaignPayload = {
                operations: [
                    {
                        create: {
                            name: campaignName,
                            advertisingChannelType: "SEARCH",
                            status: status || "PAUSED",
                            campaignBudget: budgetResourceName,
                            manualCpc: {
                                enhancedCpcEnabled: false,
                            },
                        },
                    },
                ],
            };

            const response = await axios.post<CampaignResponse>(
                `https://googleads.googleapis.com/v19/customers/${customerId}/campaigns:mutate`,
                campaignPayload,
                {
                    headers: this.getHeaders(),
                }
            );

            if (!response.data?.results?.[0]?.resourceName) {
                throw new Error('Failed to create campaign: No resource name returned');
            }

            // Extract campaign ID from resourceName (format: "customers/123/campaigns/456")
            const resourceName = response.data.results[0].resourceName;
            const campaignId = resourceName.split('/')[3];

            return {
                data: response.data,
                campaignId: campaignId
            };
        } catch (error) {
            console.error('Error in createCampaignWithBudget:', error);
            throw (error);
        }
    }

    /**
     * Fetches campaign details
     */
    async fetchCampaignDetails(customerId: string, campaignId: string): Promise<CampaignDetails> {
        try {
            // Start with basic fields and add more as needed
            const query = `
                SELECT 
                    campaign.id, 
                    campaign.name, 
                    campaign.status,
                    campaign.advertising_channel_type,
                    campaign_budget.amount_micros,
                    campaign_budget.name,
                    campaign_budget.status
                FROM campaign 
                WHERE campaign.id = ${campaignId}`;

            const response = await axios.post(
                `https://googleads.googleapis.com/v19/customers/${customerId}/googleAds:search`,
                query,
                {
                    headers: this.getHeaders(),
                }
            );

            if (!response.data?.results?.[0]) {
                throw new Error(`No campaign found with ID: ${campaignId}`);
            }

            return response.data.results[0];
        } catch (error) {
            console.error('Error in fetchCampaignDetails:', error);
            throw (error);
        }
    }

    /**
     * Returns the headers needed for Google Ads API requests
     */
    private getHeaders() {
        return {
            'Authorization': this.authToken,
            'developer-token': this.devToken,
            'Content-Type': 'application/json',
            'login-customer-id': this.loginCustomerId
        };
    }

    /**
     * Validates user request parameters
     */
    private validateUserRequest(request: UserRequest) {
        if (!request.customerId) {
            throw new Error('Customer ID is required');
        }
        if (!request.budgetName) {
            throw new Error('Budget name is required');
        }
        if (!request.budgetAmountMicros || isNaN(parseInt(request.budgetAmountMicros))) {
            throw new Error('Valid budget amount is required');
        }
        if (!request.campaignName) {
            throw new Error('Campaign name is required');
        }
    }
}

// Usage example
async function main() {
    try {
        const googleAdsService = new GoogleAdsService();

        // Example: Create a new campaign
        const userRequest: UserRequest = {
            budgetName: 'My Budget',
            budgetAmountMicros: '10000000', // $10
            campaignName: 'My Campaign',
            status: "PAUSED",
            customerId: '1172025472'
        };

        const result = await googleAdsService.createCampaign(userRequest);
        console.log('Campaign created successfully:', result);

        // Example: Fetch details for an existing campaign
        const existingCampaignId = '22591966460';
        const details = await googleAdsService.fetchCampaignDetails(userRequest.customerId, existingCampaignId);
        console.log('Campaign details:', details);
    } catch (error) {
        console.error('Error in main:', error);
    }
}

// Uncomment to run the example
// main();