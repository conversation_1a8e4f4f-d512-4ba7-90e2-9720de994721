import { PrismaClient } from '../../../../../prisma/generated/client';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import { getCustomChannels, readTokensFromFile, saveTokensToFile } from '@/utils/functions';
import { google } from 'googleapis';
import path from 'path';
import fs from 'fs/promises';

const prisma = new PrismaClient();
const TOKENS_PATH = path.join(process.cwd(), 'public', 'tokens.json');

export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        const code = searchParams.get('code');
        const customChannelsTrue = searchParams.get("customChannels");

        const oauth2Client = new google.auth.OAuth2(
            process.env.CLIENT_ID,
            process.env.CLIENT_SECRET,
            process.env.REDIRECT_URI
        );

        const authUrl = oauth2Client.generateAuthUrl({
            access_type: 'offline',
            scope: [
                'https://www.googleapis.com/auth/adsense.readonly',
                'https://www.googleapis.com/auth/adsense',
            ],
            prompt: 'consent',
        });

        let tokens = await readTokensFromFile(TOKENS_PATH, fs);

        if (!code && !tokens) {
            return NextResponse.redirect(authUrl);
        }

        if (code) {
            const { tokens: newTokens } = await oauth2Client.getToken(code);
            oauth2Client.setCredentials(newTokens);
            tokens = newTokens;
            await saveTokensToFile({ ...newTokens, authUrl }, TOKENS_PATH, fs);

            return NextResponse.json(
                {
                    message: 'Authorization successful',
                    tokens: newTokens,
                },
                { status: 200 }
            );
        }

        if (tokens?.expiry_date && tokens.expiry_date <= Date.now()) {
            oauth2Client.setCredentials({
                refresh_token: tokens.refresh_token,
            });

            const { credentials } = await oauth2Client.refreshAccessToken();
            tokens = { ...tokens, ...credentials };
            await saveTokensToFile(tokens, TOKENS_PATH, fs);
        }

        oauth2Client.setCredentials(tokens);

        const userSettings = await prisma.adminUserSetting.findFirst({});
        if (!userSettings?.AdsAccountId || !userSettings?.AdsClientId) {
            return NextResponse.json(
                { message: 'AdSense account ID or client ID not found' },
                { status: 404 }
            );
        }

        const exampleAdClientId =
            userSettings?.AdsAccountId + "/" + userSettings?.AdsClientId;
            
        if (customChannelsTrue === "true") {

            const customChannels = await getCustomChannels(
                oauth2Client,
                exampleAdClientId,
                google
            );

            if (!customChannels || customChannels.length === 0) {
                return NextResponse.json({ message: 'No custom channels found' }, { status: 404 });
            }

            for (const channel of customChannels) {
                const { name, displayName, reportingDimensionId } = channel;

                if (!name) continue;

                await prisma.channals.upsert({
                    where: { Name: name },
                    update: {
                        DisplayName: displayName || null,
                        ReportingDimensionId: reportingDimensionId || null,
                        Active: true,
                        UpdatedAt: new Date(),
                    },
                    create: {
                        Name: name,
                        DisplayName: displayName || null,
                        ReportingDimensionId: reportingDimensionId || null,
                        Active: true,
                        CreatedAt: new Date(),
                    },
                });
            }

            return NextResponse.json({ message: 'Channels synced successfully' });
        }

        return NextResponse.json({ message: 'Custom channels sync not requested' });

    } catch (error) {
        console.error('[ERROR] Failed to sync channels:', error);
        return NextResponse.json(
            {
                error: 'Failed to sync channels',
                details: error instanceof Error ? error.message : error,
                stack: process.env.NODE_ENV === 'development' ? error : undefined,
            },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}