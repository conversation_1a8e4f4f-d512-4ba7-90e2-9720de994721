"use client";
import axios from 'axios';
import { useRouter } from 'next/navigation';
import React, { useEffect } from 'react';
import { toast } from 'react-toastify';
import Swal from 'sweetalert2';

const Logout = () => {
    const router = useRouter();

    const handleLogOut = async () => {
        try {
            Swal.fire({
                title: "Are you sure?",
                text: "You will be logged out!",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#5750f1",
                showCloseButton: true,
                cancelButtonColor: "#d33",
                confirmButtonText: "Yes, log out!",
            }).then(async (result) => {
                if (result.isConfirmed) {
                    const response = await axios.post('/api/auth/Logout', {}, {
                        withCredentials: true, // Correct usage
                    });
                    if (response.data.success) {
                         localStorage.removeItem("user");
                        toast.success(response.data.message || "Logged Out Successfully");
                        router.push('/');
                    } else {
                        toast.error("Logout failed");
                    }
                }
            });

        } catch (error) {
            console.error("Logout error:", error);
            toast.error("An error occurred during logout");
        }
    };

    useEffect(() => {
        // handleLogOut();
    }, []);

    return <div>Logging out...</div>;
};

export default Logout;
