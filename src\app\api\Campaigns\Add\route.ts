import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import { GoogleAdsService } from '@/services/googleCampaignService';

export async function POST(req: NextRequest) {
    try {
        // Authenticate user
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Parse request body
        const body = await req.json();
        const { budgetName, budgetAmountMicros, campaignName, status, customerId } = body;

        // Validate required fields
        if (!budgetName || !budgetAmountMicros || !campaignName || !customerId) {
            return NextResponse.json(
                { error: 'Missing required campaign fields' },
                { status: 400 }
            );
        }

        // Create campaign
        const googleAdsService = new GoogleAdsService();
        const result = await googleAdsService.createCampaign({
            budgetName,
            budgetAmountMicros,
            campaignName,
            status,
            customerId,
        });

        return NextResponse.json({ success: true, result }, { status: 200 });

    } catch (error: any) {
        console.error('Error in POST /api/campaign/create:', error);
        const message = error?.response?.data ?? error.message ?? 'Unknown error occurred';
        return NextResponse.json(
            {
                error: 'Failed to create campaign',
                details: message,
            },
            { status: 500 }
        );
    }
}