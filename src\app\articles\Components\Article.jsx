"use client";
import React, { useCallback, useEffect, useRef, useState, useMemo } from "react";
import dynamic from "next/dynamic";
import axios from "axios";
import { FaPlus } from "react-icons/fa";
const JoditEditor = dynamic(() => import("jodit-react"), { ssr: false });
import { convertToBase64, fetchImage } from "@/utils/functions";
import Swal from "sweetalert2";
import CustomDataTable from "@/components/DataTable/CustomDataTable";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";
import MultiSelectDropdown from "@/components/FormElements/Dropdowns/MultiSelectDropdown";
import InputGroup from "@/components/FormElements/InputGroup";
import { TextAreaGroup } from "@/components/FormElements/InputGroup/text-area";
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
} from "@mui/material";
import { <PERSON><PERSON> } from "@/components/ui-elements/button";
import { decodeJWT } from "@/utils/functions";
import { CloseIcon } from "@/assets/icons";
import Checkbox from "@/components/FormElements/checkbox";

// JoditEditor configuration (unchanged)
const joditConfig = {
  height: 300,
  minHeight: 200,
  maxHeight: 300,
  uploader: { insertImageAsBase64URI: true },
  toolbarAdaptive: false,
  buttons: [
    "bold",
    "italic",
    "underline",
    "strikethrough",
    "|",
    "ul",
    "ol",
    "|",
    "outdent",
    "indent",
    "|",
    "font",
    "fontsize",
    "brush",
    "paragraph",
    "|",
    "image",
    "video",
    "table",
    "link",
    "|",
    "align",
    "undo",
    "redo",
    "|",
    "hr",
    "eraser",
    "copyformat",
    "|",
    "source",
    "fullsize",
  ],
  style: {
    border: "1px solid #e5e7eb",
    borderRadius: "8px",
    overflowY: "auto",
  },
};

const shortJoditConfig = {
  height: 100,
  minHeight: 120,
  maxHeight: 150,
  uploader: { insertImageAsBase64URI: true },
  toolbarAdaptive: false,
  buttons: ["bold", "italic", "underline", "ul", "ol", "link", "erase"],
  style: {
    border: "1px solid #e5e7eb",
    borderRadius: "8px",
    overflowY: "auto",
  },
};

const reverseFieldMapping = {
  title: "Title",
  url: "url",
  description: "description",
  shortDescription: "shortDescription",
  metatitle: "metatitle",
  metadescription: "metadescription",
  metakeys: "metakeys",
  hashtag: "hashtag",
  customChannal: "customChannal",
  styleId: "styleId",
  adrelatedsearches: "adrelatedsearches",
  remark: "remark",
  category: "category",
  userName: "userName",
  domain: "domain",
  subdomain: "subdomain",
  published: "Published",
  showArticle: "ShowArticle",
  showAds: "ShowsAds",
  image: "image",
  categoryName: "Category",
  customChannalName: "CustomChannal",
  channelId: "CustomChannalId",
  campaignCount: "CampaignCount",
  status: "Published",
  userName: "UserName",
  domainName: "DomainName",
  subDomainName: "SubDomainName",
};

const ArticlePage = () => {
  const [categories, setCategories] = useState([]);
  const [modalShow, setModalShow] = useState(false);
  const [articles, setArticles] = useState([]);
  const [editId, setEditId] = useState(null);
  const [showLoader, setShowLoader] = useState(false);
  const [published, setPublished] = useState(true);
  const [showArticle, setShowArticle] = useState(false);
  const [showAds, setShowAds] = useState(false);
  const [formdataImage, setFormdataImage] = useState("");
  const [assignChannels, setAssignChannels] = useState([]);
  const [base64Image, setBase64Image] = useState("");
  const [users, setUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [isSuperAdmin, setIsSuperAdmin] = useState(true);
  const [domains, setDomains] = useState([]);
  const [selectedDomain, setSelectedDomain] = useState("");
  const [subdomains, setSubdomains] = useState([]);
  const [selectedSubdomain, setSelectedSubdomain] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [editSlugMode, setEditSlugMode] = useState(false);
  const [campaigns, setCampaigns] = useState([]);
  const [selectedCampaignIds, setSelectedCampaignIds] = useState([]);
  const [styleIds, setStyleIds] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const dropdownRef = useRef(null);
  const [description, setDescription] = useState("");
  const [shortDescription, setShortDescription] = useState("");
  const [orderBy, setOrderBy] = useState("");
  const [order, setOrder] = useState("asc");
  const [userData, setUserData] = useState(null);
  const [campaignSearchTerm, setCampaignSearchTerm] = useState("");
  const [showCampaignModal, setShowCampaignModal] = useState(false);
  const [selectedArticleForCampaigns, setSelectedArticleForCampaigns] = useState(null);
  const [articleCampaigns, setArticleCampaigns] = useState([]);
  const [campaignModalLoading, setCampaignModalLoading] = useState(false);
  const [showAddCampaignForm, setShowAddCampaignForm] = useState(false);
  const [campaignFormData, setCampaignFormData] = useState({
    budgetName: "",
    budgetAmountMicros: "",
    campaignName: "",
    customerId: "",
  });
  const [customerOptions, setCustomerOptions] = useState([]);

  // Add mounted state to prevent hydration issues
  const [isMounted, setIsMounted] = useState(false);

  // Define columns with stable structure to prevent hydration issues
  const columns = useMemo(() => {
    const baseColumns = [
      { id: "title", label: "Title" },
      { id: "categoryName", label: "Category" },
      { id: "showAds", label: "Show Ads" },
      { id: "customChannalName", label: "Custom Channel" },
      { id: "campaignCount", label: "Campaigns" },
      { id: "status", label: "Status" },
      { id: "domainName", label: "Domain Name" },
    ];

    // Only add userName column after component is mounted and user is super admin
    if (isMounted && isSuperAdmin) {
      baseColumns.splice(1, 0, { id: "userName", label: "User Name" });
    }

    return baseColumns;
  }, [isMounted, isSuperAdmin]);

  const mapResponseToFrontend = (article) => {
    // Construct full domain name from DomainName and SubDomainName
    const fullDomainName = article.SubDomainName && article.DomainName
      ? `${article.SubDomainName}.${article.DomainName}`
      : article.DomainName || "No domain";

      // Construct custom channel name with ID
    const channelName = article.ChannelName || article.customChannalName || "No channel";
    const channelId = article.CustomChannalId || "No ID";
    const formattedChannelName = article.CustomChannalId
      ? `${channelName} [${channelId}]`
      : channelName;
    return {
      id: article.Id || article.id,
      title: article.Title || article.title || "No Title",
      url: article.Url || article.url || "",
      description: article.Description || article.description || "",
      shortDescription: article.ShortDescription || article.shortDescription || "",
      metatitle: article.MetaTitle || article.metatitle || "",
      metadescription: article.MetaDescription || article.metadescription || "",
      metakeys: article.MetaKeys || article.metakeys || "",
      hashtag: article.Hashtag || article.hashtag || "",
      customChannal:
        article.CustomChannalId &&
        (article.ChannelName || article.customChannalName)
          ? {
              Id: article.CustomChannalId,
              displayName: article.ChannelName || article.customChannalName,
              reportingDimensionId: `ga:${article.CustomChannalId}`,
            }
          : null,
      styleId: article.StyleId || article.styleId || "",
      adrelatedsearches: article.AdRelatedSearches || article.adrelatedsearches || "7",
      remark: article.Remark || article.remark || "",
      published: !!article.Published || !!article.published,
      showArticle: !!article.ShowArticle || !!article.showArticle,
      showAds: !!article.ShowsAds || !!article.showsAds,
      image: article.Image || article.image || "",
      userId: article.User_Id_Settings || article.userId || "",
      userName: article.UserName || article.userName || "Unknown",
      category: article.CategoryName
        ? {
            Id: article.CategoryId || article.category?.Id,
            Name: article.CategoryName,
          }
        : null,
      domain: article.DomainName
        ? {
            Id: article.DomainId || article.domain?.Id,
            name: article.DomainName,
            showUrlName: article.DomainName,
          }
        : null,
      subdomain: article.SubDomainName
        ? {
            Id: article.SubDomainId || article.subdomain?.Id,
            name: article.SubDomainName,
          }
        : null,
      campaigns:
        article.Campaigns?.map((c) => ({
          SNo: c.SNo,
          Name: c.Name,
          Description: c.Description || "",
        })) || [],
      categoryName: article.CategoryName || "No category",
      showAds: article.ShowsAds || article.showsAds ? "True" : "False",
      customChannalName: formattedChannelName || "No channel",
      channelId: article.CustomChannalId || "No ID",
      status: article.Published || article.published ? "Published" : "Draft",
      campaignCount: article.CampaignCount || 0,
      domainName: fullDomainName, // Use constructed full domain
    };
  };

  // Rest of the component remains unchanged
  const defaultValuesForm = {
    title: { val: "", err: "" },
    category: { val: "", err: "" },
    url: { val: "", err: "" },
    description: { val: "", err: "" },
    shortDescription: { val: "", err: "" },
    metatitle: { val: "", err: "" },
    metadescription: { val: "", err: "" },
    metakeys: { val: "", err: "" },
    customChannal: { val: "", err: "" },
    hashtag: { val: "", err: "" },
    styleId: { val: "", err: "" },
    adrelatedsearches: { val: "7", err: "" },
    remark: { val: "", err: "" },
    campaigns: { val: [], err: "" },
    domain: { val: "", err: "" },
    subdomain: { val: "", err: "" },
  };
  const optionalFields = [
    "remark",
    "metatitle",
    "metadescription",
    "metakeys",
    "hashtag",
  ];
  // Helper function for client-side operations
  const showAlert = (config) => {
    if (isMounted) {
      Swal.fire(config);
    }
  };

  // Mount effect to prevent hydration issues
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Authentication effect - only runs after component is mounted
  useEffect(() => {
    if (!isMounted) return;

    const accessToken = localStorage.getItem("accessToken");
    if (accessToken) {
      try {
        const decoded = decodeJWT(accessToken);
        console.log(decoded);

        setUserData(decoded);
        // Set user type based on JWT payload
        setIsSuperAdmin(decoded.User_Type === "Super Admin");
      } catch (error) {
        console.error("Error decoding JWT:", error);
        // Optionally redirect to login or show error
        showAlert({
          icon: "error",
          title: "Authentication Error",
          text: "Invalid access token. Please log in again.",
          timer: 3000,
          showConfirmButton: false,
        });
      }
    } else {
      // Handle missing token (e.g., redirect to login)
      showAlert({
        icon: "warning",
        title: "Session Expired",
        text: "Please log in to continue.",
        timer: 3000,
        showConfirmButton: false,
      });
    }
  }, [isMounted]);
  const [formData, setFormData] = useState(defaultValuesForm);

  const validateField = (name, value) => {
    if (optionalFields.includes(name)) return "";
    return value
      ? ""
      : `${name.replace(/([A-Z])/g, " $1").trim()} field is required.`;
  };

  const fetchArticles = useCallback(async () => {
    try {
      setShowLoader(true);
      let url = `/api/article/get?page=${page + 1}&length=${rowsPerPage}`;
      if (searchTerm) url += `&q=${encodeURIComponent(searchTerm)}`;
      if (selectedUser) url += `&userId=${encodeURIComponent(selectedUser)}`;
      if (selectedCategory)
        url += `&CategoryId=${encodeURIComponent(selectedCategory)}`;
      if (orderBy) {
        const backendField = reverseFieldMapping[orderBy] || orderBy;
        url += `&orderBy=${encodeURIComponent(backendField)}&orderDir=${order}`;
      }

      const response = await axios.get(url, { withCredentials: true });
      const articlesData = response.data.data || [];
      const totalItems = response.data.pagination?.recordsFiltered || 0;

      const mappedArticles = articlesData.map(mapResponseToFrontend);
      setArticles(mappedArticles);
      setTotalCount(totalItems);
    } catch (error) {
      console.error("Error fetching articles:", error);
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch articles",
        timer: 3000,
        showConfirmButton: false,
      });
      setArticles([]);
      setTotalCount(0);
    } finally {
      setShowLoader(false);
    }
  }, [
    page,
    rowsPerPage,
    searchTerm,
    selectedUser,
    selectedCategory,
    orderBy,
    order,
  ]);
  const fetchCategories = useCallback(async (params = {}) => {
    try {
      setShowLoader(true);
      const response = await axios.get("/api/category/getAllCategory", {
        params: { q: params.search || "" },
        withCredentials: true,
      });
      if (response.status === 200) {
        setCategories(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch categories",
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setShowLoader(false);
    }
  }, []);

  const fetchCampaigns = useCallback(async (subdomainId) => {
    if (!subdomainId) {
      setCampaigns([]);
      setSelectedCampaignIds([]);
      setFormData((prev) => ({
        ...prev,
        campaigns: { val: [], err: "" },
      }));
      return;
    }

    try {
      const response = await axios.get(
        `/api/Campaigns/GetDropdown?subdomainId=${subdomainId}`,
        {
          withCredentials: true,
        },
      );
      setCampaigns(response.data.data || []);
    } catch (error) {
      console.error("Error fetching campaigns:", error);
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch campaigns",
        timer: 3000,
        showConfirmButton: false,
      });
      setCampaigns([]);
    }
  }, []);

  const fetchStyleIds = useCallback(async () => {
    try {
      if (isSuperAdmin) {
        // For super admin, use the regular API
        const response = await axios.get("/api/StyleIds/get", {
          withCredentials: true,
        });
        const styleIdsData = (response.data.data || []).map(style => ({
          ...style,
          Id: String(style.Id) // Ensure string type for consistency
        }));
        setStyleIds(styleIdsData);
        console.log("Style IDs for super admin:", styleIdsData);
      } else {
        // For regular users, get only assigned style IDs
        const response = await axios.get(`/api/AssignUserStyleId/Get?Id=${userData?.Id}&length=-1`, {
          withCredentials: true,
        });

        const assignedStyleIds = response.data.data || [];

        // Transform the assigned style IDs to match the expected format
        const transformedStyleIds = assignedStyleIds.map(styleMapping => ({
          Id: String(styleMapping.Id), // Ensure string type
          Name: styleMapping.Name,
          StyleId: styleMapping.StyleId,
          Prefix: styleMapping.Prefix
        }));

        setStyleIds(transformedStyleIds);
        console.log("Transformed Style IDs for regular user:", transformedStyleIds);
      }
    } catch (error) {
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch style IDs",
        timer: 3000,
        showConfirmButton: false,
      });
      setStyleIds([]);
    }
  }, [isSuperAdmin, userData?.Id]);

  const fetchUsers = async () => {
    // Only fetch users if the current user is a super admin
    if (isSuperAdmin) {
      try {
        const response = await axios.get("/api/adminuser/GetDropdown", {
          withCredentials: true,
        });
        setUsers(response.data.data);
      } catch (error) {
        showAlert({
          icon: "error",
          title: "Error",
          text: "Failed to fetch users",
          timer: 3000,
          showConfirmButton: false,
        });
      }
    }else{
      return [];
    }
  };

  const fetchChannels = useCallback(async () => {
    try {
      const response = await axios.get("/api/Channals/GetDropdown", {
        withCredentials: true,
      });
      setAssignChannels(response.data.data);
    } catch (error) {
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch channels",
        timer: 3000,
        showConfirmButton: false,
      });
    }
  }, []);

  const fetchCustomerOptions = useCallback(async () => {
    try {
      const response = await axios.get("/api/AccountDetails/GetDropDown", {
        withCredentials: true,
      });
      setCustomerOptions(response.data.data || []);
    } catch (error) {
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch customer options",
        timer: 3000,
        showConfirmButton: false,
      });
    }
  }, []);

  const fetchDomains = async () => {
    try {
      if (isSuperAdmin) {
        // For super admin, use the regular dropdown API to get all domains
        const response = await axios.get("/api/Domain/GetDropDown", {
          withCredentials: true,
        });
        setDomains(response.data.data || []);
      } else {
        // For regular users, get domains from their assigned subdomains using the mapping API
        const response = await axios.get(`/api/AssignUserSubDomain/Get?Id=${userData?.Id}&length=-1`, {
          withCredentials: true,
        });

        const assignedSubdomains = response.data.data || [];

        // Extract unique domains from assigned subdomains
        const uniqueDomains = [];
        const domainMap = new Map();

        assignedSubdomains.forEach(subdomain => {
          if (subdomain.DomainId && subdomain.DomainName && !domainMap.has(subdomain.DomainId)) {
            domainMap.set(subdomain.DomainId, {
              Id: subdomain.DomainId,
              Name: subdomain.DomainName
            });
            uniqueDomains.push({
              Id: subdomain.DomainId,
              Name: subdomain.DomainName
            });
          }
        });

        setDomains(uniqueDomains);
      }
    } catch (error) {
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to load domains",
        timer: 3000,
        showConfirmButton: false,
      });
      setDomains([]);
    }
  };

  const fetchSubdomains = async () => {
    if (!selectedDomain) {
      setSubdomains([]);
      return;
    }
    try {
      if (isSuperAdmin) {
        // For super admin, use the regular dropdown API
        const response = await axios.get(
          `/api/SubDomain/GetDropDown?DomainId=${selectedDomain}`,
          { withCredentials: true },
        );
        setSubdomains(response.data.data || []);
      } else {
        // For regular users, get only assigned subdomains for the selected domain
        const response = await axios.get(`/api/AssignUserSubDomain/Get?Id=${userData?.Id}&length=-1`, {
          withCredentials: true,
        });

        const assignedSubdomains = response.data.data || [];

        // Filter subdomains for the selected domain
        const domainSubdomains = assignedSubdomains
          .filter(subdomain => subdomain.DomainId === selectedDomain)
          .map(subdomain => ({
            Id: subdomain.SubDomainId || subdomain.Id,
            Name: subdomain.SubDomainName,
            Url: subdomain.SubDomainUrl
          }));

        setSubdomains(domainSubdomains);
      }
    } catch (error) {
      showAlert({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to load subdomains",
        timer: 3000,
        showConfirmButton: false,
      });
      setSubdomains([]);
      setSelectedSubdomain("");
    }
  };



  const handleCampaignCountClick = useCallback(async (rowData) => {
    const articleId = rowData?.id || rowData?.Id;

    if (!articleId) {
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: "Invalid article ID. Please ensure the article has a valid ID.",
        timer: 3000,
        showConfirmButton: false,
      });
      return;
    }

    try {
      setCampaignModalLoading(true);
      setSelectedArticleForCampaigns(rowData);
      setShowCampaignModal(true);

      // Fetch both campaigns and customer options
      const [campaignsResponse] = await Promise.all([
        axios.get(`/api/Campaigns/GetData?articleId=${articleId}`, { withCredentials: true }),
        fetchCustomerOptions()
      ]);

      setArticleCampaigns(campaignsResponse.data.data || []);
    } catch (error) {
      console.error("Error fetching article campaigns:", error);
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch campaigns",
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setCampaignModalLoading(false);
    }
  }, []);

  const handleAddCampaign = useCallback(
    async (e) => {
      e.preventDefault();

      try {
        setCampaignModalLoading(true);

        await axios.post(
          "/api/Campaigns/Add",
          campaignFormData,
          { withCredentials: true },
        );

        await Swal.fire({
          icon: "success",
          title: "Success",
          text: "Campaign created successfully",
          timer: 2000,
          showConfirmButton: false,
        });

        setCampaignFormData({
          budgetName: "",
          budgetAmountMicros: "",
          campaignName: "",
          customerId: "",
        });
        setShowAddCampaignForm(false);

        if (selectedArticleForCampaigns) {
          await handleCampaignCountClick(selectedArticleForCampaigns);
        }
      } catch (error) {
        console.error("Error creating campaign:", error);
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to create campaign",
          timer: 3000,
          showConfirmButton: false,
        });
      } finally {
        setCampaignModalLoading(false);
      }
    },
    [campaignFormData, selectedArticleForCampaigns, handleCampaignCountClick],
  );

  const handleCampaignFormChange = useCallback((e) => {
    const { name, value } = e.target;
    setCampaignFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  }, []);

  const handleFormSubmit = async (e) => {
    e.preventDefault();

    const updatedFormData = {
      ...formData,
      title: {
        val: formData.title.val,
        err: validateField("title", formData.title.val),
      },
      category: {
        val: formData.category.val,
        err: validateField("category", formData.category.val),
      },
      description: {
        val: description,
        err: validateField("description", description),
      },
      shortDescription: {
        val: shortDescription,
        err: validateField("shortDescription", shortDescription),
      },
      domain: {
        val: selectedDomain,
        err: validateField("domain", selectedDomain),
      },
      subdomain: {
        val: selectedSubdomain,
        err: validateField("subdomain", selectedSubdomain),
      },
      customChannal: {
        val: formData.customChannal.val,
        err: validateField("customChannal", formData.customChannal.val),
      },
    };

    const hasErrors = Object.values(updatedFormData).some((field) => field.err);
    if (hasErrors) {
      setFormData(updatedFormData);
      await Swal.fire({
        icon: "error",
        title: "Validation Error",
        text: "Please fill all required fields",
        timer: 3000,
        showConfirmButton: false,
      });
      return;
    }

    setShowLoader(true);
    try {
      const formDataPayload = new FormData();

      formDataPayload.append("Title", formData.title.val);
      formDataPayload.append("Category", formData.category.val);

      const urlSlug =
        formData.url.val.split("/").pop() || autoslug(formData.title.val);
      formDataPayload.append("Url", urlSlug);

      if (description) formDataPayload.append("Description", description);
      if (shortDescription)
        formDataPayload.append("ShortDescription", shortDescription);
      if (formData.metatitle.val)
        formDataPayload.append("MetaTitle", formData.metatitle.val);
      if (formData.metadescription.val)
        formDataPayload.append("MetaDescription", formData.metadescription.val);
      if (formData.metakeys.val)
        formDataPayload.append("MetaKeys", formData.metakeys.val);
      if (formData.customChannal.val)
        formDataPayload.append("CustomChannal", formData.customChannal.val);
      if (formData.styleId.val)
        formDataPayload.append("StyleId", formData.styleId.val);
      formDataPayload.append(
        "AdRelatedSearches",
        formData.adrelatedsearches.val || "7",
      );
      if (formData.remark.val)
        formDataPayload.append("Remark", formData.remark.val);
      if (formData.hashtag.val)
        formDataPayload.append("Hashtag", formData.hashtag.val);

      formDataPayload.append("Published", String(published));
      formDataPayload.append("ShowArticle", String(showArticle));
      formDataPayload.append("ShowsAds", String(showAds));

      if (selectedDomain) formDataPayload.append("Domain", selectedDomain);
      if (selectedSubdomain)
        formDataPayload.append("SubDomain", selectedSubdomain);

      if (formdataImage && typeof formdataImage === "object") {
        formDataPayload.append("file", formdataImage);
      }

      if (editId) {

        formDataPayload.append("Id", editId);
      }

      formDataPayload.append(
        "CampaignIds",
        JSON.stringify(selectedCampaignIds),
      );

      const endpoint = editId ? "/api/article/edit" : "/api/article/add";
      const method = editId ? "PUT" : "POST";

      const response = await axios({
        url: endpoint,
        method,
        data: formDataPayload,
        headers: { "Content-Type": "multipart/form-data" },
        withCredentials: true,
      });

      if ([200, 201].includes(response.status)) {
        resetFormState();
        await Swal.fire({
          icon: "success",
          title: "Success",
          text: editId
            ? "Article updated successfully"
            : "Article created successfully",
          timer: 3000,
          showConfirmButton: false,
        });
        fetchArticles();
        setModalShow(false);
      }
    } catch (error) {
      console.error("Error saving article:", error.response?.data || error);
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to save article",
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setShowLoader(false);
    }
  };

  const filteredCampaigns = campaignSearchTerm
    ? articleCampaigns.filter((campaign) =>
        campaign.Name.toLowerCase().includes(campaignSearchTerm.toLowerCase())
      )
    : articleCampaigns;

  const resetFormState = () => {
    setEditId(null);
    setFormData(defaultValuesForm);
    setDescription("");
    setShortDescription("");
    setFormdataImage("");
    setBase64Image("");
    setPublished(true);
    setShowArticle(false);
    setShowAds(false);
    setSelectedDomain("");
    setSelectedSubdomain("");
    setSelectedCampaignIds([]);
    setSearchTerm("");
    setEditSlugMode(false);
  };

  const handleChange = (e) => {
    const { name, value, files } = e.target || e;
    if (name === "title") {
      const shouldUpdateSlug = !editId || editSlugMode;
      setFormData((prev) => ({
        ...prev,
        [name]: { val: value, err: validateField(name, value) },
        ...(shouldUpdateSlug && { url: { val: autoslug(value), err: "" } }),
      }));
      return;
    }
    if (name === "url") {
      setFormData((prev) => ({
        ...prev,
        url: { val: autoslug(value), err: "" },
      }));
      return;
    }
    if (files && files.length > 0) {
      setFormdataImage(files[0]);
      setBase64Image("");
      return;
    }
    setFormData((prev) => ({
      ...prev,
      [name]: { val: value, err: validateField(name, value) },
    }));
  };

  const handleCampaignSelect = (campaignIds) => {
    const numericIds = campaignIds.map((id) => Number(id));
    setSelectedCampaignIds(numericIds);
    setFormData((prev) => ({
      ...prev,
      campaigns: { val: numericIds, err: "" },
    }));
  };

  const handleDescriptionChange = (newContent) => {
    setDescription(newContent);
    setFormData((prev) => ({
      ...prev,
      description: {
        val: newContent,
        err: validateField("description", newContent),
      },
    }));
  };

  const handleShortDescriptionChange = (newContent) => {
    setShortDescription(newContent);
    setFormData((prev) => ({
      ...prev,
      shortDescription: {
        val: newContent,
        err: validateField("shortDescription", newContent),
      },
    }));
  };

  const handleChannelSelect = (channel) => {
    setFormData((prev) => ({
      ...prev,
      customChannal: {
        val: channel?.Id || "",
        err: validateField("customChannal", channel?.Id || ""),
      },
    }));
  };

  const handleEditArticle = useCallback(
    async (rowData) => {
      try {
        // setShowLoader(true);

        const loadPromises = [];

        if (assignChannels.length === 0) {
          loadPromises.push(fetchChannels());
        }
        if (domains.length === 0) {
          loadPromises.push(fetchDomains());
        }
        if (styleIds.length === 0) {
          loadPromises.push(fetchStyleIds());
        }

        await Promise.all(loadPromises);

        const response = await axios.get(
          `/api/article/GetById?id=${rowData.id}`,
          {
            withCredentials: true,
          },
        );

        if (response.data.success && response.data.data?.length > 0) {
          const article = response.data.data[0];
          setModalShow(true);
          setEditId(article.Id);
          setEditSlugMode(false);

          const channelId = article.ChannelId || "";

          setPublished(!!article.Published);
          setShowArticle(!!article.ShowArticle);
          setShowAds(!!article.ShowsAds);

          setDescription(article.Description || "");
          setShortDescription(article.ShortDescription || "");

          // setTimeout(() => {
            const formDataToSet = {
              ...defaultValuesForm,
              title: { val: article.Title || "", err: "" },
              category: { val: article.CategoryId || "", err: "" },
              url: { val: article.Url || "", err: "" },
              description: { val: article.Description || "", err: "" },
              shortDescription: {
                val: article.ShortDescription || "",
                err: "",
              },
              metatitle: { val: article.MetaTitle || "", err: "" },
              metadescription: { val: article.MetaDescription || "", err: "" },
              metakeys: { val: article.MetaKeys || "", err: "" },
              hashtag: { val: article.Hashtag || "", err: "" },
              customChannal: { val: channelId, err: "" },
              styleId: { val: String(article.StyleId || ""), err: "" },
              adrelatedsearches: {
                val: article.AdRelatedSearches || "7",
                err: "",
              },
              remark: { val: article.Remark || "", err: "" },
              campaigns: {
                val: article.Campaigns?.map((c) => c.SNo) || [],
                err: "",
              },
              domain: { val: article.DomainId || "", err: "" },
              subdomain: { val: article.SubDomainId || "", err: "" },
            };

            setFormData(formDataToSet);
          // }, 100);

          setSelectedDomain(article.DomainId || "");

          if (article.DomainId) {
            try {
              const subdomainResponse = await axios.get(
                `/api/SubDomain/GetDropDown?DomainId=${article.DomainId}`,
                { withCredentials: true },
              );
              setSubdomains(subdomainResponse.data.data || []);
              setSelectedSubdomain(article.SubDomainId || "");
            } catch (error) {
              console.error("Error loading subdomains:", error);
              setSubdomains([]);
              setSelectedSubdomain("");
            }
          } else {
            setSelectedSubdomain("");
          }

          if (article.SubDomainId) {
            await fetchCampaigns(article.SubDomainId);
            if (article.Campaigns && article.Campaigns.length > 0) {
              const campaignIds = article.Campaigns.map((c) => c.SNo);
              setSelectedCampaignIds(campaignIds);
            }
          } else {
            setSelectedCampaignIds([]);
          }

          if (article.Image) {
            setFormdataImage(
              fetchImage(
                article.Image.includes("cloudinary") ? "cloud" : "small",
                article.Image,
              ),
            );
          } else {
            setFormdataImage("");
          }
          setBase64Image("");
        }
      } catch (error) {
        console.error("Error fetching article:", error);
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: "Failed to load article data",
          timer: 3000,
          showConfirmButton: false,
        });
      } finally {
        setShowLoader(false);
      }
    },
    [
      fetchCampaigns,
      assignChannels,
      domains,
      styleIds,
      fetchChannels,
      fetchDomains,
      fetchStyleIds,
    ],
  );
  const handleDeleteArticle = useCallback(
    async (rowData) => {
      if (!isMounted) return;

      const result = await Swal.fire({
        icon: "warning",
        title: "Confirm Deletion",
        text: "Are you sure you want to delete this article?",
        showCancelButton: true,
        confirmButtonColor: "#5750f1",
        cancelButtonColor: "#d33",
        showCloseButton: true,
        confirmButtonText: "Yes, delete it!",
      });

      if (result.isConfirmed) {
        try {
          setShowLoader(true);
          await axios.delete("/api/article/delete", {
            data: { id: rowData.id },
            withCredentials: true,
            headers: { "Content-Type": "application/json" },
          });

          showAlert({
            icon: "success",
            title: "Success",
            text: "Article deleted successfully",
            timer: 3000,
            showConfirmButton: false,
          });

          fetchArticles();
        } catch (error) {
          console.error("Error deleting article:", error);
          showAlert({
            icon: "error",
            title: "Error",
            text: error?.response?.data?.error || "Failed to delete article",
            timer: 3000,
            showConfirmButton: false,
          });
        } finally {
          setShowLoader(false);
        }
      }
    },
    [fetchArticles, isMounted],
  );

  const handleViewArticle = useCallback((rowData) => {
    if (!isMounted) return;

    if (!rowData.published) {
      showAlert({
        icon: "warning",
        title: "Warning",
        text: "Article is not published yet",
        timer: 3000,
        showConfirmButton: false,
      });
      return;
    }
    const link = makeLinkRedirect(rowData);
    window.open(link, "_blank");
  }, [isMounted]);

  const makeLinkRedirect = (article) => {
    if (!article.published) return "/";
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL_CLIENT_USER_SITE;

    if (article.domain?.showUrlName) {
      const fullDomain = article.subdomain?.name
        ? `${article.subdomain.name}.${article.domain.showUrlName}`
        : article.domain.showUrlName;
      const slug = article.url.split("/").pop();
      const channelId =
        article.customChannal?.reportingDimensionId?.split(":")[1] || "";
      return `https://${fullDomain}/${slug}?channel=${channelId}&mode=light`;
    }

    const channelId =
      article.customChannal?.reportingDimensionId?.split(":")[1] || "";
    return `${baseUrl}/${article.url.split("/").pop()}?channel=${channelId}&mode=light`;
  };

  const autoslug = (title) =>
    title ? title.replace(/[^a-zA-Z0-9]+/g, "-").toLowerCase() : "";

  const imageConvert = async () => {
    if (formdataImage && typeof formdataImage === "object") {
      try {
        const base64 = await convertToBase64(formdataImage);
        setBase64Image(base64);
      } catch (error) {
        console.error("Failed to convert image:", error);
        setBase64Image("");
        showAlert({
          icon: "error",
          title: "Error",
          text: "Failed to process image. Please select a valid image file.",
          timer: 3000,
          showConfirmButton: false,
        });
      }
    }
  };

  const renderSlugField = () => (
    <div className="mb-4">
      <label className="mb-2 block text-sm font-bold text-dark dark:text-white">
        URL
      </label>
      <div className="flex flex-wrap items-center gap-2">
        <span className="rounded-md border border-stroke bg-gray-100 px-4 py-3 text-dark dark:border-dark-3 dark:bg-dark-2 dark:text-white">
          {selectedDomain
            ? `https://${
                selectedSubdomain
                  ? `${subdomains.find((s) => s.Id === selectedSubdomain)?.Name || ""}.`
                  : ""
              }${domains.find((d) => d.Id === selectedDomain)?.Name || ""}/`
            : `${process.env.NEXT_PUBLIC_BASE_URL_CLIENT_USER_SITE}/`}
        </span>
        <input
          type="text"
          name="url"
          value={formData.url.val.split("/").pop()}
          onChange={handleChange}
          className="min-w-[200px] flex-1 rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition-all duration-200 focus:border-primary focus:ring-2 focus:ring-primary/20 dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary"
          readOnly={!editSlugMode}
        />
        <label className="flex items-center gap-2">
          <Checkbox
            label="Edit Slug"
            name="editSlug"
            checked={editSlugMode}
            onChange={() => setEditSlugMode(!editSlugMode)}
            withIcon="check"
            withBg
            radius="default"
          />
        </label>
      </div>
      {formData.url.err && (
        <p className="mt-1 text-sm text-red-500">{formData.url.err}</p>
      )}
    </div>
  );

  useEffect(() => {
    fetchCategories();
    if (isMounted && isSuperAdmin) {
      fetchUsers();
    }
    fetchArticles();
  }, [fetchArticles, isMounted, isSuperAdmin]);

  useEffect(() => {
    if (modalShow) {
      fetchChannels();
      fetchStyleIds();
      fetchDomains();
    }
  }, [modalShow, fetchChannels, fetchStyleIds]);

  useEffect(() => {
    fetchSubdomains();
  }, [selectedDomain]);

  useEffect(() => {
    if (selectedSubdomain) {
      fetchCampaigns(selectedSubdomain);
    } else {
      setCampaigns([]);
      setSelectedCampaignIds([]);
      setFormData((prev) => ({
        ...prev,
        campaigns: { val: [], err: "" },
      }));
    }
  }, [selectedSubdomain, fetchCampaigns]);

  useEffect(() => {
    imageConvert();
  }, [formdataImage]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        // Handle dropdown close if needed
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);



  // Prevent hydration issues by not rendering until mounted
  if (!isMounted) {
    return (
      <div className="rounded-[10px] border border-stroke bg-white p-4 shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card sm:p-6">
        <div className="flex items-center justify-center py-8">
          <div className="text-lg text-gray-500">Loading...</div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="rounded-[10px] border border-stroke bg-white p-4 shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card sm:p-6">
        <div className="mb-5 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <h2 className="text-2xl font-bold text-dark dark:text-white">
            Articles
          </h2>
          <div className="flex flex-col items-center gap-4 sm:flex-row">
            {/* Only render user dropdown for super admin */}
            {isSuperAdmin && (
              <div className="w-full sm:w-64">
                <SearchableDropdown
                  options={[{ Id: "", Name: "All Users" }, ...users]}
                  placeholder="Select User..."
                  value={selectedUser}
                  onChange={(user) => {
                    setSelectedUser(user?.Id || "");
                    setPage(0);
                  }}
                  displayKey="Name"
                  idKey="Id"
                />
              </div>
            )}
            <div className="w-full sm:w-64">
              <SearchableDropdown
                options={[{ Id: "", Name: "All Categories" }, ...categories]}
                placeholder="Select Category..."
                value={selectedCategory}
                onChange={(category) => {
                  setSelectedCategory(category?.Id || "");
                  setPage(0);
                }}
                displayKey="Name"
                idKey="Id"
              />
            </div>
            <Button
              type="button"
              label="Add Article"
              variant="primary"
              shape="rounded"
              className="flex items-center justify-center gap-2"
              icon={<FaPlus size={14} />}
              onClick={() => {
                setModalShow(true);
                resetFormState();
              }}
            />
          </div>
        </div>

        <CustomDataTable
          isLoading={showLoader}
          columns={columns}
          rows={articles}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          page={page}
          rowsPerPage={rowsPerPage}
          onPageChange={setPage}
          onRequestSort={(_, property) => {
            const isAsc = orderBy === property && order === "asc";
            setOrder(isAsc ? "desc" : "asc");
            setOrderBy(property);
          }}
          onRowsPerPageChange={(value) => {
            setRowsPerPage(value);
            setPage(0);
          }}
          totalCount={totalCount}
          order={order}
          orderBy={orderBy}
          onView={handleViewArticle}
          onEdit={handleEditArticle}
          onDelete={handleDeleteArticle}
          onCampaignCountClick={handleCampaignCountClick}
          handleCampaignCountClick={handleCampaignCountClick}
        />
      </div>
      <Dialog
        open={modalShow}
        onClose={() => {
          setModalShow(false);
          resetFormState();
        }}
        fullWidth
        maxWidth="lg"
        slotProps={{
          paper: {
            sx: {
              maxHeight: "90vh",
            },
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            color: "white",
            py: 2,
            px: 3,
          }}
          className="bg-primary text-white"
        >
          <span style={{ fontSize: "1.25rem", fontWeight: 500 }}>
            {editId ? "Edit Article" : "Add Article"}
          </span>
          <IconButton
            aria-label="close"
            onClick={() => {
              setModalShow(false);
              resetFormState();
            }}
            sx={{
              color: "white",
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent dividers sx={{ py: 3, px: 3 }}>
          <form onSubmit={handleFormSubmit} className="flex flex-1 flex-col">
            <div className="mb-4 flex flex-col gap-4 lg:flex-row">
              <SearchableDropdown
                label="Domain"
                options={domains}
                placeholder="Select Domain..."
                value={selectedDomain}
                onChange={(domain) => {
                  setSelectedDomain(domain?.Id || "");
                  setSelectedSubdomain("");
                  setFormData((prev) => ({
                    ...prev,
                    domain: {
                      val: domain?.Id || "",
                      err: validateField("domain", domain?.Id || ""),
                    },
                    subdomain: { val: "", err: "" },
                  }));
                }}
                error={formData.domain.err}
                displayKey="Name"
                idKey="Id"
                required
              />
              <SearchableDropdown
                label="Subdomain"
                options={subdomains}
                placeholder="Select Subdomain..."
                value={selectedSubdomain}
                onChange={(subdomain) => {
                  setSelectedSubdomain(subdomain?.Id || "");
                  setFormData((prev) => ({
                    ...prev,
                    subdomain: {
                      val: subdomain?.Id || "",
                      err: validateField("subdomain", subdomain?.Id || ""),
                    },
                  }));
                }}
                error={formData.subdomain.err}
                displayKey="Name"
                idKey="Id"
                disabled={!selectedDomain || !subdomains.length}
                required
              />
            </div>
            <div className="mb-4 flex flex-col gap-4 lg:flex-row">
              <div className="w-full">
                <InputGroup
                  label="Article Title"
                  name="title"
                  type="text"
                  placeholder="Enter Article Title"
                  value={formData.title.val}
                  handleChange={handleChange}
                  required
                  className="w-full"
                  active={!!formData.title.err}
                />
                {formData.title.err && (
                  <p className="mt-1 text-sm text-red-500">
                    {formData.title.err}
                  </p>
                )}
              </div>
              <div className="w-full">
                <SearchableDropdown
                  label="Category"
                  options={categories}
                  placeholder="Select Category..."
                  value={formData.category.val}
                  onChange={(category) => {
                    setFormData((prev) => ({
                      ...prev,
                      category: {
                        val: category?.Id || "",
                        err: validateField("category", category?.Id || ""),
                      },
                    }));
                  }}
                  error={formData.category.err}
                  displayKey="Name"
                  idKey="Id"
                  required
                />
              </div>
            </div>
            {renderSlugField()}
            <div className="mb-4">
              <label className="mb-2 block text-sm font-bold text-dark dark:text-white">
                Short Description
              </label>
              <JoditEditor
                value={shortDescription}
                config={shortJoditConfig}
                onBlur={handleShortDescriptionChange}
                onChange={() => {}}
              />
              {formData.shortDescription.err && (
                <p className="mt-1 text-sm text-red-500">
                  {formData.shortDescription.err}
                </p>
              )}
            </div>
            <div className="mb-4">
              <label className="mb-2 block text-sm font-bold text-dark dark:text-white">
                Description
              </label>
              <JoditEditor
                value={description}
                config={joditConfig}
                onBlur={handleDescriptionChange}
                onChange={() => {}}
              />
              {formData.description.err && (
                <p className="mt-1 text-sm text-red-500">
                  {formData.description.err}
                </p>
              )}
            </div>
            <div className="mb-4 flex flex-col gap-4 sm:flex-row">
              <div className="w-full sm:w-1/3">
                <TextAreaGroup
                  label="Meta Title"
                  name="metatitle"
                  value={formData.metatitle.val}
                  handleChange={handleChange}
                  placeholder="Enter Meta Title"
                  rows={2}
                />
              </div>
              <div className="w-full sm:w-1/3">
                <TextAreaGroup
                  label="Meta Description"
                  name="metadescription"
                  value={formData.metadescription.val}
                  handleChange={handleChange}
                  placeholder="Enter Meta Description"
                  rows={2}
                />
              </div>
              <div className="w-full sm:w-1/3">
                <TextAreaGroup
                  label="Meta Keys"
                  name="metakeys"
                  value={formData.metakeys.val}
                  handleChange={handleChange}
                  placeholder="Enter Meta Keys"
                  rows={2}
                />
              </div>
            </div>
            <div className="mb-4">
              <TextAreaGroup
                label="Hashtag"
                name="hashtag"
                value={formData.hashtag.val}
                handleChange={(e) =>
                  handleChange({
                    target: {
                      name: "hashtag",
                      value: e.target.value.replace(/[ ,]/g, "#"),
                    },
                  })
                }
                placeholder="Enter Hashtags"
                rows={2}
              />
            </div>
            <div className="mb-4 flex flex-col gap-4 lg:flex-row">
              <SearchableDropdown
                label="Channel Settings"
                options={assignChannels}
                placeholder="Select Channel..."
                value={formData.customChannal.val}
                onChange={handleChannelSelect}
                error={formData.customChannal.err}
                displayKey="DisplayName"
                idKey="Id"
                required
              />
              <SearchableDropdown
                label="Style Id"
                options={styleIds}
                placeholder="Search Style ID..."
                value={formData.styleId.val}
                onChange={(style) => {
                  console.log("Style selected:", style);
                  console.log("Current styleIds:", styleIds);
                  console.log("Current formData.styleId.val:", formData.styleId.val);
                  const styleId = String(style?.Id || "");
                  setFormData((prev) => ({
                    ...prev,
                    styleId: {
                      val: styleId,
                      err: validateField("styleId", styleId),
                    },
                  }));
                }}
                error={formData.styleId.err}
                displayKey="Name"
                idKey="Id"
              />
            </div>
            <div className="mb-4">
              <MultiSelectDropdown
                label="Campaigns"
                options={campaigns}
                value={selectedCampaignIds}
                onChange={handleCampaignSelect}
                placeholder="Search Campaigns..."
                displayKey="Name"
                idKey="SNo"
                showSelectAll={true}
                error={formData.campaigns.err}
              />
            </div>
            <div className="mb-4">
              <InputGroup
                label="Ad Related Searches"
                name="adrelatedsearches"
                type="number"
                value={formData.adrelatedsearches.val}
                handleChange={handleChange}
                placeholder="Enter number of ads"
                required
              />
            </div>
            <div className="mb-4">
              <TextAreaGroup
                label="Remark"
                name="remark"
                value={formData.remark.val}
                handleChange={handleChange}
                placeholder="Enter Remark"
                rows={3}
              />
            </div>
            <div className="mb-4">
              <InputGroup
                type="file"
                name="Image"
                handleChange={handleChange}
                fileStyleVariant="style1"
                label="Image"
                placeholder="Image"
              />
              {(base64Image ||
                (typeof formdataImage === "string" && formdataImage)) && (
                <div className="mt-4">
                  <img
                    src={base64Image || formdataImage}
                    alt="Preview"
                    className="h-32 w-32 rounded object-cover"
                    width={128}
                    height={128}
                  />
                </div>
              )}
            </div>
            <div className="mb-6 flex flex-col gap-4 sm:flex-row">
              <label className="flex items-center gap-2">
                <Checkbox
                  label="Published"
                  name="published"
                  checked={published}
                  onChange={() => setPublished(!published)}
                  withIcon="check"
                  withBg
                  radius="default"
                />
              </label>
              <label className="flex items-center gap-2">
                <Checkbox
                  label="Show Article"
                  name="showArticle"
                  checked={showArticle}
                  onChange={() => setShowArticle(!showArticle)}
                  withIcon="check"
                  withBg
                  radius="default"
                />
              </label>
              <label className="flex items-center gap-2">
                <Checkbox
                  label="Show Ads"
                  name="showAds"
                  checked={showAds}
                  onChange={() => setShowAds(!showAds)}
                  withIcon="check"
                  withBg
                  radius="default"
                />
              </label>
            </div>
          </form>
        </DialogContent>

        <DialogActions sx={{ py: 2, px: 3 }}>
          <Button
            type="button"
            label="Cancel"
            onClick={() => {
              setModalShow(false);
              resetFormState();
            }}
            variant="dark"
            shape="rounded"
          />
          <Button
            type="submit"
            label={editId ? "Update Article" : "Add Article"}
            variant="primary"
            shape="rounded"
            disabled={showLoader}
            onClick={handleFormSubmit}
          />
        </DialogActions>
      </Dialog>

      <Dialog
        open={showCampaignModal}
        onClose={() => {
          console.log("🔒 Campaign modal onClose triggered");
          setShowCampaignModal(false);
          setSelectedArticleForCampaigns(null);
          setArticleCampaigns([]);
          setShowAddCampaignForm(false);
          setCampaignSearchTerm("");
        }}
        fullWidth
        maxWidth="md"
        slotProps={{
          paper: {
            sx: {
              maxHeight: "90vh",
              zIndex: 1300,
            },
          },
        }}
      >
        <DialogTitle
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            color: "white",
            py: 2,
            px: 3,
          }}
          className="bg-primary text-white"
        >
          <span className="text-xl font-medium">
            Campaigns for{" "}
            <span className="font-bold">
              "{selectedArticleForCampaigns?.title}"
            </span>
          </span>
          <IconButton
            aria-label="close"
            onClick={() => {
              setShowCampaignModal(false);
              setSelectedArticleForCampaigns(null);
              setArticleCampaigns([]);
              setShowAddCampaignForm(false);
              setCampaignSearchTerm("");
            }}
            sx={{
              color: "white",
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent dividers sx={{ py: 3, px: 3 }}>
          {!showAddCampaignForm ? (
            <>
              <div className="mb-4 flex items-center justify-between">
                <h3 className="text-lg font-semibold">Campaign List</h3>
                <Button
                  type="button"
                  label="Add Campaign"
                  variant="primary"
                  shape="rounded"
                  icon={<FaPlus size={14} />}
                  onClick={() => setShowAddCampaignForm(true)}
                />
              </div>

              <CustomDataTable
                isLoading={campaignModalLoading}
                columns={[
                  { id: "CampaignId", label: "Campaign ID" },
                  { id: "Name", label: "Campaign Name" },
                  { id: "CreatedAt", label: "Added Date" },
                ]}
                rows={filteredCampaigns}
                page={0}
                rowsPerPage={10}
                onPageChange={() => {}}
                onRowsPerPageChange={() => {}}
                totalCount={articleCampaigns.length}
                searchTerm={campaignSearchTerm}
                onSearchChange={setCampaignSearchTerm}
                order="asc"
                orderBy=""
                onRequestSort={() => {}}
              />
            </>
          ) : (
            <form onSubmit={handleAddCampaign}>
              <h3 className="mb-4 text-lg font-semibold">Add New Campaign</h3>

              <div className="mb-4">
                <InputGroup
                  label="Budget Name"
                  name="budgetName"
                  type="text"
                  value={campaignFormData.budgetName}
                  handleChange={handleCampaignFormChange}
                  placeholder="Enter budget name"
                  required
                />
              </div>

              <div className="mb-4">
                <InputGroup
                  label="Budget Amount (Micros)"
                  name="budgetAmountMicros"
                  type="number"
                  value={campaignFormData.budgetAmountMicros}
                  handleChange={handleCampaignFormChange}
                  placeholder="Enter budget amount in micros"
                  required
                />
              </div>

              <div className="mb-4">
                <InputGroup
                  label="Campaign Name"
                  name="campaignName"
                  type="text"
                  value={campaignFormData.campaignName}
                  handleChange={handleCampaignFormChange}
                  placeholder="Enter campaign name"
                  required
                />
              </div>

              <div className="mb-4">
                <SearchableDropdown
                  label="Customer ID"
                  options={customerOptions}
                  placeholder="Select Customer..."
                  value={campaignFormData.customerId}
                  onChange={(customer) => {
                    setCampaignFormData((prev) => ({
                      ...prev,
                      customerId: customer?.AccountId || "",
                    }));
                  }}
                  displayKey="DescriptiveName"
                  idKey="AccountId"
                  required
                />
              </div>
            </form>
          )}
        </DialogContent>

        <DialogActions sx={{ py: 2, px: 3 }}>
          {showAddCampaignForm ? (
            <>
              <Button
                type="button"
                label="Cancel"
                onClick={() => setShowAddCampaignForm(false)}
                variant="dark"
                shape="rounded"
              />
              <Button
                type="submit"
                label={campaignModalLoading ? "Creating..." : "Create Campaign"}
                variant="primary"
                shape="rounded"
                disabled={campaignModalLoading}
                onClick={handleAddCampaign}
              />
            </>
          ) : (
            <Button
              type="button"
              label="Close"
              onClick={() => {
                setShowCampaignModal(false);
                setSelectedArticleForCampaigns(null);
                setArticleCampaigns([]);
                setCampaignSearchTerm("");
              }}
              variant="dark"
              shape="rounded"
            />
          )}
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ArticlePage;