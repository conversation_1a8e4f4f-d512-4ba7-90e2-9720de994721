import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

export async function POST() {
  const cookieStore = await cookies();
  const refreshToken = cookieStore.get('refreshToken')?.value;

  if (!refreshToken) {
    return NextResponse.json({ error: 'No refresh token' }, { status: 401 });
  }

  try {
    const decoded = jwt.verify(refreshToken, process.env.JWT_SECRET as string) as any;

    const newAccessToken = jwt.sign(
      {
        id: decoded.id,
        email: decoded.email,
        user_type: decoded.user_type,
      },
      process.env.JWT_SECRET as string,
      { expiresIn: '15m' }
    );

    cookieStore.set('accessToken', newAccessToken, { httpOnly: true });

    return NextResponse.json({ success: true });
  } catch (error) {
    return NextResponse.json({ error: 'Invalid refresh token' }, { status: 403 });
  }
}
