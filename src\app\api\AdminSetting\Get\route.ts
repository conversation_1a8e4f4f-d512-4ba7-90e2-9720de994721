import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function POST(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const hasBody = req.headers.get('content-length') !== '0';
        const body = hasBody ? await req.json() : null;

        // Try to find existing record
        let existingSetting = await prisma.adminUserSetting.findFirst({
            select: {
                Id: true,
                LmStyleId: true,
                DmStyleId: true,
                ChannalId: true,
                PubId: true,
                AdsAccountId: true,
                AdsClientId: true,
                HeadTagJSON: true,
                CreatedAt: true,
                UpdatedAt: true,
                CountAdsClick: true
            }
        });

        if (!existingSetting) {
            // Create if not found
            const newSetting = await prisma.adminUserSetting.create({
                data: {
                    LmStyleId: body?.LmStyleId ?? null,
                    DmStyleId: body?.DmStyleId ?? null,
                    ChannalId: body?.ChannalId ?? null,
                    PubId: body?.PubId ?? null,
                    AdsAccountId: body?.AdsAccountId ?? null,
                    AdsClientId: body?.AdsClientId ?? null,
                    HeadTagJSON: body?.HeadTagJSON ?? '{}',
                    CountAdsClick: body?.CountAdsClick ?? ""
                },
                select: {
                    Id: true,
                    LmStyleId: true,
                    DmStyleId: true,
                    ChannalId: true,
                    PubId: true,
                    AdsAccountId: true,
                    AdsClientId: true,
                    HeadTagJSON: true,
                    CreatedAt: true,
                    UpdatedAt: true,
                    CountAdsClick: true
                }
            });

            return NextResponse.json(
                {
                    success: true,
                    message: "Admin Setting created sucessfully",
                    data: newSetting,
                }
            );
        }

        if (body) {
            // Update existing record
            const updatedSetting = await prisma.adminUserSetting.update({
                where: { Id: existingSetting.Id },
                data: {
                    LmStyleId: body.LmStyleId && body.LmStyleId !== '' ? body.LmStyleId : existingSetting.LmStyleId,
                    DmStyleId: body.DmStyleId && body.DmStyleId !== '' ? body.DmStyleId : existingSetting.DmStyleId,
                    ChannalId: body.ChannalId && body.ChannalId !== '' ? body.ChannalId : existingSetting.ChannalId,
                    PubId: body.PubId && body.PubId !== '' ? body.PubId : existingSetting.PubId,
                    AdsAccountId: body.AdsAccountId && body.AdsAccountId !== '' ? body.AdsAccountId : existingSetting.AdsAccountId,
                    AdsClientId: body.AdsClientId && body.AdsClientId !== '' ? body.AdsClientId : existingSetting.AdsClientId,
                    HeadTagJSON: body.HeadTagJSON ?? existingSetting.HeadTagJSON,
                    CountAdsClick: body.CountAdsClick ?? existingSetting.CountAdsClick
                },
                select: {
                    Id: true,
                    LmStyleId: true,
                    DmStyleId: true,
                    ChannalId: true,
                    PubId: true,
                    AdsAccountId: true,
                    AdsClientId: true,
                    HeadTagJSON: true,
                    CreatedAt: true,
                    UpdatedAt: true,
                    CountAdsClick: true
                }
            });

            return NextResponse.json(
                {
                    success: true,
                    message: "Admin Setting updated sucessfully",
                    data: updatedSetting,
                }
            );
        }

        // Return existing if no update body
        return NextResponse.json(
            {
                success: true,
                data: existingSetting,
            }
        );
    } catch (error) {
        console.error("Error handling admin user setting:", error);
        return NextResponse.json(
            {
                error: "Failed to process admin user setting",
                details: process.env.NODE_ENV === 'development' ? error : undefined
            },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}
