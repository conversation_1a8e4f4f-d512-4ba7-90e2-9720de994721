import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Get subdomainId from query parameters
        const { searchParams } = new URL(req.url);
        const subdomainId = searchParams.get('subdomainId');

        if (!subdomainId) {
            return NextResponse.json(
                { error: 'Subdomain ID is required' },
                { status: 400 }
            );
        }

        // Find the subdomain
        const subdomain = await prisma.subDomain.findUnique({
            where: {
                Id: subdomainId,
            },
            select: {
                AccountId: true
            }
        });

        if (!subdomain) {
            return NextResponse.json(
                { error: 'Subdomain not found' },
                { status: 404 }
            );
        }

        // If AccountId is null, return empty array
        if (!subdomain.AccountId) {
            return NextResponse.json({
                success: true,
                data: [],
            });
        }

        // Get campaigns for this account
        const campaigns = await prisma.ads_Campaigns.findMany({
            where: {
                AccountId: Number(subdomain.AccountId) 
            },
            select: {
                SNo: true,
                CampaignId: true,
                Name: true
            },
        });

        // Convert BigInt to string for JSON serialization
        const safeCampaigns = campaigns.map(campaign => ({
            ...campaign,
            SNo: campaign.SNo.toString(),
            CampaignId: campaign.CampaignId?.toString()
        }));

        return NextResponse.json({
            success: true,
            data: safeCampaigns,
        });

    } catch (error) {
        console.error("Error fetching campaigns:", error);
        return NextResponse.json(
            { error: "Failed to fetch campaigns" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}