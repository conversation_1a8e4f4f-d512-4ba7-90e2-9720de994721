import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        const Id = searchParams.get("id");

        let where: any = {
            IsDeleted: false,
            Id: Id
        };

        const users = await prisma.adminUser.findMany({
            where,
            select: {
                Id: true,
                Name: true,
                Email: true,
                Number: true,
                User_Type: true,
                Status: true,
                Block: true,
                ProfilePic: true,
                AccessExpiration: true
            }
        });

    
        return NextResponse.json({
            success: true,
            data: users            
        });

    } catch (error) {
        console.error("Error fetching users:", error);
        return NextResponse.json(
            { error: "User Not Found" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}