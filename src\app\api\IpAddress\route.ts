import fs from 'fs';
import { prisma } from '../../../lib/prisma';
import { NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid'; // Import UUID for generating unique IDs

export async function POST() {
    try {
        const filePath = 'C:/Users/<USER>/Downloads/ipsum.txt';
        const content = fs.readFileSync(filePath, 'utf-8');

        // Get all lines (including header/comment lines)
        const lines = content.split('\n');

        const data = [];

        for (const line of lines) {
            const trimmed = line.trim();
            if (!trimmed) continue;

            // Try to split line by whitespace to get IP and number, if possible
            const parts = trimmed.split(/\s+/);

            if (parts.length >= 2) {
                // Normal IP + number line
                const [ip, count] = parts;
                const noOfBlackList = parseInt(count, 10);
                data.push({
                    Id: uuidv4(), // Generate a unique ID
                    IpAddress: ip,
                    NoOfBlackList: isNaN(noOfBlackList) ? 0 : noOfBlackList,
                });
            } else {
                // If line does not have two parts (e.g., comment), store whole line as IpAddress and 0
                data.push({
                    Id: uuidv4(), // Generate a unique ID
                    IpAddress: trimmed,
                    NoOfBlackList: 0,
                });
            }
        }

        const result = await prisma.ipAddress.createMany({
            data,
            skipDuplicates: true,
        });

        return NextResponse.json({ message: `Inserted ${result.count} lines.` });
    } catch (error) {
        console.error(error);
        return NextResponse.json({ error: 'Import failed' }, { status: 500 });
    }
}