import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';
import sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';
import fs from 'fs';
import path from 'path';

// const UPLOAD_DIR = path.join(process.cwd(), 'public/uploads');
const UPLOAD_DIR = process.env.IMAGE_STORAGE_PATH || "/var/www/images";

export async function POST(req: NextRequest) {

    type AuthenticatedUser = {
        Id: string;
    };
    const user = await verifyToken(req) as AuthenticatedUser;
    if (!user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    try {
        const formData = await req.formData();

        // Extract and type cast all form fields
        const textFields = {
            Title: formData.get('Title') as string,
            Category: formData.get('Category') as string,
            Url: formData.get('Url') as string | null,
            Description: formData.get('Description') as string | null,
            Published: formData.get('Published') === 'true',
            ShowArticle: formData.get('ShowArticle') === 'true',
            ShowsAds: formData.get('ShowsAds') === 'true',
            MetaTitle: formData.get('MetaTitle') as string | null,
            MetaDescription: formData.get('MetaDescription') as string | null,
            MetaKeys: formData.get('MetaKeys') as string || '',
            CustomChannal: formData.get('CustomChannal') as string | null,
            StyleId: formData.get('StyleId') as string | null,
            AdRelatedSearches: formData.get('AdRelatedSearches') as string | null,
            Remark: formData.get('Remark') as string || '',
            ShortDescription: formData.get('ShortDescription') as string | null,
            Domain: formData.get('Domain') as string | null,
            SubDomain: formData.get('SubDomain') as string | null,
            CampaignIds: JSON.parse(formData.get('CampaignIds') as string || '[]') as number[],
            User_Id_Settings: user.Id
        };

        // Validation
        if (!textFields.Title || !textFields.Category) {
            return NextResponse.json(
                { error: "Title and Category are required" },
                { status: 400 }
            );
        }

        // Check for existing article
        const existing = await prisma.articleDetails.findFirst({
            where: { Title: textFields.Title }
        });

        if (existing) {
            return NextResponse.json(
                { error: "Article title already exists" },
                { status: 409 }
            );
        }

        // Handle file upload
        const file = formData.get('file') as File | null;
        let imagePath: string | null = null;

        if (file) {
            try {
                const buffer = Buffer.from(await file.arrayBuffer());
                const mimeType = file.type;
                const originalExtension = mimeType.split('/')[1] as keyof sharp.FormatEnum || 'jpg';
                const uniqueId = uuidv4();

                if (!fs.existsSync(UPLOAD_DIR)) {
                    fs.mkdirSync(UPLOAD_DIR, { recursive: true });
                }

                // Process image versions in parallel
                const [originalBuffer, smallBuffer, mediumBuffer] = await Promise.all([
                    sharp(buffer).toBuffer(),
                    sharp(buffer)
                        .resize(300, null, { fit: 'inside', withoutEnlargement: true })
                        .toFormat(originalExtension, { quality: 80 })
                        .toBuffer(),
                    sharp(buffer)
                        .resize(720, null, { fit: 'inside', withoutEnlargement: true })
                        .toFormat(originalExtension, { quality: 80 })
                        .toBuffer()
                ]);

                // Save all versions
                const baseFileName = `${uniqueId}.${originalExtension}`;
                const versions = [
                    { suffix: '', buffer: originalBuffer },
                    { suffix: '_small', buffer: smallBuffer },
                    { suffix: '_medium', buffer: mediumBuffer }
                ];

                await Promise.all(versions.map(({ suffix, buffer }) => {
                    const fileName = `${uniqueId}${suffix}.${originalExtension}`;
                    const filePath = path.resolve(UPLOAD_DIR, fileName);
                    return fs.promises.writeFile(filePath, buffer);
                }));

                imagePath = `${baseFileName}`;
            } catch (error: unknown) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown image processing error';
                console.error("Image processing error:", errorMessage);
                return NextResponse.json(
                    { error: "Failed to process image", details: errorMessage },
                    { status: 500 }
                );
            }
        }

        // Generate URL-friendly name
        const ShowUrlName = textFields.Title
            .replace(/[^a-zA-Z0-9]+/g, "-")
            .toLowerCase();

        // Normalize campaign IDs
        const normalizedCampaignIds = Array.isArray(textFields.CampaignIds)
            ? textFields.CampaignIds.filter(id => id != null && !isNaN(Number(id)))
            : [];

        const articleData = {
            Title: textFields.Title,
            Category: textFields.Category,
            Url: textFields.Url || null,
            Description: textFields.Description || null,
            Published: textFields.Published,
            ShowArticle: textFields.ShowArticle,
            ShowsAds: textFields.ShowsAds,
            MetaTitle: textFields.MetaTitle || textFields.Title,
            MetaDescription: textFields.MetaDescription || textFields.Description || null,
            MetaKeys: textFields.MetaKeys,
            CustomChannal: textFields.CustomChannal,
            StyleId: textFields.StyleId,
            AdRelatedSearches: textFields.AdRelatedSearches,
            Remark: textFields.Remark,
            Image: imagePath,
            ShowUrlName,
            ShortDescription: textFields.ShortDescription,
            Domain: textFields.Domain,
            SubDomain: textFields.SubDomain,
            User_Id_Settings: textFields.User_Id_Settings
        };

        // Prisma infers types automatically here
        const newArticle = await prisma.articleDetails.create({
            data: articleData
        });

        // Create campaign mappings if any campaign IDs provided
        if (normalizedCampaignIds.length > 0) {
            await prisma.articleCampaignMappings.createMany({
                data: normalizedCampaignIds.map(campaignId => ({
                    ArticleId: newArticle.Id,
                    CampaignId: campaignId,
                    CreatedAt: new Date(),
                })),
                skipDuplicates: true
            });
        }

        // Fetch the complete article with relations if needed
        const articleWithRelations = await prisma.articleDetails.findUnique({
            where: { Id: newArticle.Id },
            include: {
                ArticleCampaignMappings: {
                    select: {
                        CampaignId: true
                    }
                }
            }
        });

        return NextResponse.json({
            success: true,
            message: "Article created successfully",
            article: articleWithRelations,
            campaignMappings: normalizedCampaignIds.length
        }, { status: 201 });

    } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error("Error in article creation:", errorMessage);
        return NextResponse.json(
            { error: "Failed to create article", details: errorMessage },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}