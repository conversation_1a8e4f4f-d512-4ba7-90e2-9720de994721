import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';


export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const search = searchParams.get("q");
        const subDomainId = searchParams.get("Id");

        // Get sorting parameters with validation
        const orderBy = searchParams.get("orderBy") || "CreatedAt";
        const orderDir = (searchParams.get("orderDir") || "asc").toLowerCase() as 'asc' | 'desc';

        // Define allowed sortable fields (must match your Prisma model exactly)
        const allowedOrderFields = ['CreatedAt', 'Id', 'UserName', 'UserEmail', 'UserType'];
        const sanitizedOrderBy = allowedOrderFields.includes(orderBy) ? orderBy : 'CreatedAt';

        if (!subDomainId) {
            return NextResponse.json(
                { error: "SubDomainId parameter is required" },
                { status: 400 }
            );
        }

        let skip: number | undefined;
        let limit: number | undefined;

        if (length == -1) {
            skip = undefined;
            limit = undefined;
        }
        else {
            const skipCount = (start == 1 ? 0 : start - 1) * length;
            limit = length;
            skip = (skipCount > 0 ? skipCount : 0);
        }
        const baseFilter: any = {
            SubDomainId: subDomainId,
        };

        if (search) {
            baseFilter.AdminUser = {
                OR: [
                    { Name: { contains: search, mode: 'insensitive' } },
                    { Email: { contains: search, mode: 'insensitive' } }
                ]
            };
        }

        const recordsTotal = await prisma.subDomainUserMappings.count({
            where: {
                SubDomainId: subDomainId
            }
        });

        const recordsFiltered = await prisma.subDomainUserMappings.count({
            where: baseFilter
        });

        // Create proper orderBy clause
        let prismaOrderBy = {};
        if (['UserName', 'UserEmail'].includes(sanitizedOrderBy)) {
            // Sorting by related AdminUser fields
            prismaOrderBy = {
                AdminUser: {
                    [sanitizedOrderBy.replace('User', '')]: orderDir
                }
            };
        } else {
            // Sorting by direct fields
            prismaOrderBy = {
                [sanitizedOrderBy]: orderDir
            };
        }

        const mappings = await prisma.subDomainUserMappings.findMany({
            where: baseFilter,
            skip,
            take: limit,
            orderBy: prismaOrderBy,
            include: {
                AdminUser: {
                    select: {
                        Id: true,
                        Name: true,
                        Email: true,
                        User_Type: true
                    }
                }
            }
        });

        // Transform data with flattened user properties
        const transformedData = mappings.map(mapping => ({
            Id: mapping.Id,
            CreatedAt: mapping.CreatedAt,
            UserName: mapping.AdminUser?.Name,
            UserEmail: mapping.AdminUser?.Email,
            UserType: mapping.AdminUser?.User_Type
        }));

        return NextResponse.json({
            success: true,
            data: transformedData,
            pagination: {
                draw: parseInt(searchParams.get("draw") || "1"),
                //recordsTotal,
                recordsFiltered,
                currentPageCount: transformedData.length,
                start,
                length,
                currentPage: start,
                totalPages: length === -1 ? 1 : Math.ceil(recordsFiltered / (length || 1)),
                hasNextPage: length === -1 ? false : start * length < recordsFiltered,
                hasPreviousPage: start > 1,
            }
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            {
                error: "Internal Server Error",
                details: process.env.NODE_ENV === 'development' ? error : undefined
            },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}