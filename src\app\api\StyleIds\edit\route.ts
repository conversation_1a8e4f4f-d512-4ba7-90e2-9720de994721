import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';


export async function PUT(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { Id, StyleId, Name, Prefix, AssignUsers } = await req.json();

        if (!Id) {
            return NextResponse.json(
                { error: "Id is required for updating a style" },
                { status: 400 }
            );
        }

        // Check if style exists
        const existingStyle = await prisma.styleIds.findFirst({
            where: { StyleId: StyleId }
        });
        console.log(existingStyle, "existingStyle");


        // Prepare update data
        const updateData: any = {};
        if (StyleId !== undefined) updateData.StyleId = StyleId;
        if (Name !== undefined) updateData.Name = Name;
        if (Prefix !== undefined) updateData.Prefix = Prefix;
        updateData.UpdatedAt = new Date();

        if (StyleId ) {
            const styleWithSameId = await prisma.styleIds.findFirst({
                where: { Id }
            });
        }

        // Validate AssignUsers if provided
        // if (AssignUsers !== undefined) {
        //     // First validate it's an array (but can be empty)
        //     if (!Array.isArray(AssignUsers)) {
        //         return NextResponse.json(
        //             { error: "AssignUsers must be an array" },
        //             { status: 400 }
        //         );
        //     }

        //     // Only validate user IDs if the array is not empty
        //     if (AssignUsers.length > 0) {
        //         const userIds = AssignUsers.map(userId => userId);
        //         const existingUsers = await prisma.AdminUser.findMany({
        //             where: {
        //                 Id: {
        //                     in: userIds
        //                 },
        //                 IsDeleted: false 
        //             },
        //             select: {
        //                 Id: true
        //             }
        //         });

        //         if (existingUsers.length !== userIds.length) {
        //             const invalidUserIds = userIds.filter(id =>
        //                 !existingUsers.some(user => user.Id === id)
        //             );
        //             return NextResponse.json(
        //                 {
        //                     error: "One or more user IDs are invalid",
        //                     invalidUserIds // Optional: return which IDs were invalid
        //                 },
        //                 { status: 400 }
        //             );
        //         }
        //     }
        // }

        if (!AssignUsers || !Array.isArray(AssignUsers) || AssignUsers.length === 0) {
            return NextResponse.json(
                { error: "AssignUsers must be a non-empty array" },
                { status: 400 }
            );
        }

        // Use a transaction to ensure both operations succeed or fail together
        const result = await prisma.$transaction(async (prismaTransaction) => {
            const updatedStyle = await prismaTransaction.styleIds.update({
                where: { Id },
                data: updateData
            });

            let updatedMappings = [];
            if (AssignUsers !== undefined) {
                // Delete existing mappings
                await prismaTransaction.styleIdUserMappings.deleteMany({
                    where: {
                        StyleId: updatedStyle.Id
                    }
                });

                // Create new mappings
                for (const userId of AssignUsers) {
                    const mapping = await prismaTransaction.styleIdUserMappings.create({
                        data: {
                            StyleId: updatedStyle.Id,
                            UserId: userId
                        }
                    });
                    updatedMappings.push(mapping);
                }
            }

            return {
                style: updatedStyle,
                mappings: updatedMappings
            };
        });

        return NextResponse.json(
            {
                success: true,
                message: "Style updated successfully",
                style: result.style,
                mappingsUpdated: AssignUsers ? result.mappings.length : 'unchanged'
            },
            { status: 200 }
        );
    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            { error: "Internal Server Error" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}

