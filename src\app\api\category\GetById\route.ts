import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);

        // Parse DataTables-style parameters
        const Id = searchParams.get("id");

        let where: any = {
            IsDeleted: false,
            Id: Id
        };


        // Fetch paginated data
        const categories = await prisma.category.findMany({
            where,
            select: {
                Id: true,
                Name: true,
                ShowUrlName: true,
                CreatedAt: true,
            },

        });

        return NextResponse.json({
            success: true,
            data: categories,
        });

    } catch (error) {
        console.error('Error fetching categories:', error);
        return NextResponse.json({ error: 'Category Not Found' }, { status: 500 });
    } finally {
        await prisma.$disconnect();
    }
}
