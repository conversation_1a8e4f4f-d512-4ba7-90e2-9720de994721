import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';


export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const search = searchParams.get("q");
        const orderBy = searchParams.get("orderBy") || "CreatedAt";
        const orderDir = searchParams.get("orderDir") || "asc";
        const styleId = searchParams.get("Id");


        let skip: number | undefined;
        let limit: number | undefined;

        if (length == -1) {
            skip = undefined;
            limit = undefined;
        }
        else {
            const skipCount = (start == 1 ? 0 : start - 1) * length;
            limit = length;
            skip = (skipCount > 0 ? skipCount : 0);
        }
        if (!styleId) {
            return NextResponse.json(
                { error: "styleId parameter is required" },
                { status: 400 }
            );
        }

        let where: any = {
            StyleId: styleId,
            AdminUser: {
                is: {
                    IsDeleted: false
                }
            }
        };

        type OrderByType = {
            // [key: string]: any; // or more strictly: Prisma.AssignUserStyleIdOrderByWithRelationInput
        };

        // Create proper orderBy clause for relations
        let orderByClause: OrderByType = {};
        const orderField = orderBy.toLowerCase();


        if (orderField === "name") {
            orderByClause = { AdminUser: { Name: orderDir.toLowerCase() as 'asc' | 'desc' } };
        } else if (orderField === "email") {
            orderByClause = { AdminUser: { Email: orderDir.toLowerCase() as 'asc' | 'desc' } };
        } else if (orderField === "usertype") {
            orderByClause = { AdminUser: { User_Type: orderDir.toLowerCase() as 'asc' | 'desc' } };
        } else if (orderField === "createdat") {
            orderByClause = { CreatedAt: orderDir.toLowerCase() as 'asc' | 'desc' };
        } else {
            orderByClause = { [orderBy]: orderDir.toLowerCase() as 'asc' | 'desc' };
        }

        if (search) {
            where.AdminUser.is.OR = [
                { Name: { contains: search, mode: 'insensitive' } },
                { Email: { contains: search, mode: 'insensitive' } },
                { User_Type: { contains: search, mode: 'insensitive' } }
            ];
        }

        // Get counts
        const [recordsTotal, recordsFiltered] = await Promise.all([
            prisma.styleIdUserMappings.count({
                where: { StyleId: styleId }
            }),
            prisma.styleIdUserMappings.count({ where })
        ]);

        // Get paginated data
        const mappings = await prisma.styleIdUserMappings.findMany({
            where,
            skip,
            take: limit,
            orderBy: orderByClause,
            include: {
                AdminUser: {
                    select: {
                        Id: true,
                        Name: true,
                        Email: true,
                        User_Type: true
                    }
                }
            }
        });

        // Transform data
        const transformedData = mappings.map(mapping => ({
            Id: mapping.Id,
            CreatedAt: mapping.CreatedAt,
            UserId: mapping.AdminUser?.Id,
            UserName: mapping.AdminUser?.Name,
            UserEmail: mapping.AdminUser?.Email,
            UserType: mapping.AdminUser?.User_Type
        }));
        const totalPages = length === -1 ? 1 : Math.ceil(recordsFiltered / (length || 1));
        return NextResponse.json({
            success: true,
            data: transformedData,
            pagination: {
                //recordsTotal,
                recordsFiltered,
                currentPageCount: transformedData.length,
                start,
                length,
                currentPage: start,
                totalPages: totalPages,
                hasNextPage: length === -1 ? false : start * length < recordsFiltered,
                hasPreviousPage: start > 1,
            }
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            {
                error: "Internal Server Error",
                details: error instanceof Error ? error.message : String(error)
            },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}