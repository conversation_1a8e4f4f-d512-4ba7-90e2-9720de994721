import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const search = searchParams.get("q");
        const userId = searchParams.get("Id");
        const draw = parseInt(searchParams.get("draw") || "1");

        if (!userId) {
            return NextResponse.json(
                { error: "userId parameter is required" },
                { status: 400 }
            );
        }

        let skip: number | undefined;
        let limit: number | undefined;

        if (length == -1) {
            skip = undefined;
            limit = undefined;
        }
        else {
            const skipCount = (start == 1 ? 0 : start - 1) * length;
            limit = length;
            skip = (skipCount > 0 ? skipCount : 0);
        }
        // Get sorting parameters
        const orderBy = searchParams.get("orderBy") || "CreatedAt";
        const orderDir = (searchParams.get("orderDir") || "asc").toLowerCase() as 'asc' | 'desc';

        // Define allowed sortable fields (must match your Prisma model)
        const allowedOrderFields = [
            'CreatedAt',
            'SubDomainName',
            'SubDomainUrl',
            'DomainName'
        ];

        // Create proper orderBy clause
        let prismaOrderBy = {};
        switch (orderBy) {
            case 'SubDomainName':
                prismaOrderBy = { SubDomain: { Name: orderDir } };
                break;
            case 'SubDomainUrl':
                prismaOrderBy = { SubDomain: { Url: orderDir } };
                break;
            case 'DomainName':
                prismaOrderBy = { SubDomain: { Domain_SubDomain_DomainToDomain: { Name: orderDir } } };
                break;
            default:
                prismaOrderBy = { [orderBy]: orderDir };
        }

        // Base filter for mappings
        let where: any = {
            UserId: userId,
            SubDomain: {
                IsDeleted: false,
                Domain_SubDomain_DomainToDomain: {
                    IsDeleted: false
                }
            }
        };

        // Add search conditions if provided
        if (search) {
            where = {
                ...where,
                SubDomain: {
                    OR: [
                        { Name: { contains: search, mode: 'insensitive' } },
                        { Url: { contains: search, mode: 'insensitive' } },
                        {
                            Domain_SubDomain_DomainToDomain: {
                                Name: { contains: search, mode: 'insensitive' }
                            }
                        }
                    ]
                }
            };
        }

        // Get total records count (unfiltered)
        const recordsTotal = await prisma.subDomainUserMappings.count({
            where
        });

        // Get filtered records count
        const recordsFiltered = await prisma.subDomainUserMappings.count({
            where
        });

        // Get paginated data
        const mappings = await prisma.subDomainUserMappings.findMany({
            where,
            skip,
            take: limit,
            orderBy: prismaOrderBy,
            include: {
                SubDomain: {
                    include: {
                        Domain_SubDomain_DomainToDomain: {
                            select: {
                                Id: true,
                                Name: true
                            }
                        }
                    }
                }
            }
        });

        // Transform the data
        const transformedData = mappings.map(mapping => ({
            Id: mapping.Id,
            CreatedAt: mapping.CreatedAt,
            UserId: mapping.UserId,
            SubDomainName: mapping.SubDomain?.Name || null,
            SubDomainUrl: mapping.SubDomain?.Url || null,
            DomainId: mapping.SubDomain?.Domain_SubDomain_DomainToDomain?.Id || null,
            DomainName: mapping.SubDomain?.Domain_SubDomain_DomainToDomain?.Name || null
        }));

        return NextResponse.json({
            success: true,
            data: transformedData,
            pagination: {
                draw,
                //recordsTotal,
                recordsFiltered,
                currentPageCount: transformedData.length,
                start,
                length,
                currentPage: start,
                totalPages: length === -1 ? 1 : Math.ceil(recordsFiltered / (length || 1)),
                hasNextPage: length === -1 ? false : start * length < recordsFiltered,
                hasPreviousPage: start > 1,
            }
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json(
            {
                error: "Internal Server Error",
                details: process.env.NODE_ENV === 'development' ?
                    error instanceof Error ? error.message : String(error)
                    : undefined
            },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}