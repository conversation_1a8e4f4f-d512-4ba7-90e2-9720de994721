import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';



export async function DELETE(req: NextRequest) {
    try {
        // Authentication check
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        // Parse request body
        const { Id } = await req.json();

        // Validate input
        if (!Id) {
            return NextResponse.json(
                { error: "SubDomain ID is required" },
                { status: 400 }
            );
        }

        // Check if subdomain exists and isn't already deleted
        const existingSubdomain = await prisma.subDomain.findUnique({
            where: { Id }
        });

        if (!existingSubdomain) {
            return NextResponse.json(
                { error: "SubDomain not found" },
                { status: 404 }
            );
        }


        // Soft delete the subdomain
        await prisma.subDomain.update({
            where: { Id },
            data: {
                IsDeleted: true,
            }
        });

        // Optionally: Delete related mappings
        await prisma.subDomainUserMappings.deleteMany({
            where: { Id: Id }
        });

        return NextResponse.json(
            {
                success: true,
                message: "SubDomain deleted successfully",
            },
            { status: 200 }
        );

    } catch (error) {
        console.error("Error deleting SubDomain:", error);
        return NextResponse.json(
            {
                error: "Internal Server Error",
                details: process.env.NODE_ENV === 'development' ? error : undefined
            },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}