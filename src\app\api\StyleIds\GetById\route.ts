import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        const Id = searchParams.get("id");

        let where: any = {
            IsDeleted: false,
            Id: Id
        };


        const stylesWithUsers = await prisma.styleIds.findMany({
            where,
            select: {
                Id: true,
                Name: true,
                Prefix: true,
                StyleId: true,
                StyleIdUserMappings: {
                    select: {
                        UserId: true
                    }
                },
                _count: {
                    select: {
                        StyleIdUserMappings: true
                    }
                }
            }
        });

        // Transform data
        const transformedData = stylesWithUsers.map(style => ({
            Id: style.Id,
            Name: style.Name,
            Prefix: style.Prefix,
            StyleId: style.StyleId,
            UserMappings: style.StyleIdUserMappings.map(mapping => mapping.UserId),
            UserCount: style._count.StyleIdUserMappings,
        }));

        return NextResponse.json({
            success: true,
            data: transformedData,
        });

    } catch (error) {
        console.error("Error processing request:", error);
        return NextResponse.json({ error: 'Style Not Found' }, { status: 500 });
    } finally {
        await prisma.$disconnect();
    }
}